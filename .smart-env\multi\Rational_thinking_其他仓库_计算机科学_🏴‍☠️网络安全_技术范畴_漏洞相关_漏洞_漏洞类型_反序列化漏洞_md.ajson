"smart_sources:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md": {"path":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0665995,-0.03039012,-0.0320711,-0.06960404,0.03305143,-0.03791193,-0.02867184,0.06651863,0.03438744,0.01631835,0.01009577,-0.04957129,0.03022223,0.06191585,0.05354504,0.0172126,-0.00862188,0.0758246,0.00721558,0.04315675,0.08796397,-0.01562169,0.01783143,-0.0914363,-0.00344913,0.03336969,0.03098376,0.00046864,-0.02680105,-0.19513114,0.00563196,-0.00278659,0.03523843,0.01678341,0.01700038,-0.02183521,0.02137866,0.03287796,-0.02738969,0.00731501,0.03036581,0.0007586,0.00374881,-0.01980487,-0.06536857,-0.1102903,-0.02946429,-0.01451905,0.02346163,-0.06765611,-0.03535866,-0.07862574,-0.03799662,-0.00919513,-0.02394756,-0.01707609,0.02205439,0.04045958,0.04073429,0.01024048,0.01979597,0.01945463,-0.20339678,0.05553471,0.0122719,0.01375383,-0.00407974,-0.02723566,-0.0028318,-0.01774417,-0.00941023,0.04149099,-0.04661461,0.03941132,0.06059157,0.05075786,-0.00785786,-0.04050544,0.00119725,-0.0520879,-0.02486136,0.02546909,0.00029858,0.0207544,-0.00868285,0.03037456,0.00745737,0.01882374,0.03894916,0.0009889,-0.01839091,-0.05262978,0.06684262,0.02917804,-0.03637036,0.04735627,0.05340272,0.02666407,-0.05480403,0.12317857,-0.0604801,-0.01959119,-0.00454545,-0.03953808,0.02091224,-0.01957828,-0.03236475,-0.08366702,0.02792594,-0.04011221,-0.04590288,0.00511423,0.06001176,-0.00441717,0.02429381,0.008921,0.00935833,-0.01574473,-0.03041008,0.01252544,-0.04129156,0.01314347,0.09022947,0.00525585,-0.00697608,0.00024111,0.02299677,0.05608715,0.03726538,0.00254609,0.04516919,0.00197553,-0.03803824,-0.03219267,-0.00010586,0.03360872,-0.04422051,0.00818855,0.00057107,-0.01459707,-0.01808054,-0.04152252,-0.01631918,-0.07100246,-0.03544642,0.04553669,-0.04486172,0.00555727,0.01212258,-0.04890346,0.00390724,0.06845737,-0.0055521,-0.01656964,-0.00026879,-0.00109465,0.06483755,0.14914326,-0.05126119,-0.04378508,0.00149867,0.01319066,-0.04849207,0.20174623,0.0287741,-0.03967357,-0.04020203,0.00856103,-0.02166355,-0.02459949,0.00224847,-0.01051103,0.03223695,-0.00484964,0.02612489,-0.04556865,-0.01717075,-0.03056096,0.00632733,0.05580384,0.0485117,-0.00647106,-0.09419917,0.04687899,0.00127275,-0.10093572,-0.08206073,-0.04214654,0.00061659,-0.05614013,-0.11947346,0.01689252,-0.02243237,-0.00249911,-0.01911305,-0.06009461,0.03571462,-0.00458518,0.0325355,-0.07656146,0.14836511,0.05798574,-0.02279755,0.02855326,-0.0302092,-0.04525108,0.0009844,-0.03584785,-0.03273787,0.03839647,-0.02536405,0.00586857,-0.0361504,0.00609168,-0.05721451,0.08689874,0.04496514,0.031065,0.03445246,0.07799416,0.01550642,-0.00322634,-0.07277595,-0.24234559,-0.05070768,-0.00835736,-0.05040225,-0.01123754,-0.00539672,0.03960456,-0.01796287,0.08637279,0.04710996,0.06163937,0.06483512,-0.0592806,-0.03809784,0.02554749,-0.01220866,0.02392283,-0.03401292,-0.03989825,0.01999409,0.00158604,0.04923389,0.00320841,0.01713825,0.03249551,-0.01524125,0.12345395,0.01530138,0.04042086,-0.01940205,0.03578957,0.06384742,0.0383567,-0.10216243,0.02281629,0.03569599,-0.03613116,-0.03254335,-0.00120739,-0.01421914,-0.02098491,0.02863933,-0.01418965,-0.10451034,-0.00217407,-0.0403419,-0.03305162,0.00433817,0.01972877,0.0690347,-0.01831909,0.00842078,0.02564379,0.02507404,0.0053793,-0.04288164,-0.04170781,-0.00407732,0.01631252,0.04703246,0.02178337,-0.00029439,0.03204021,-0.02259326,0.00008513,-0.04694153,-0.0071984,-0.03421858,0.00100447,-0.0811786,-0.07917376,0.18219733,-0.01128295,-0.00307974,0.03617804,0.00961926,-0.01424468,-0.04170048,0.00436005,-0.00875748,0.05462207,0.05050283,0.03851524,0.02360127,-0.00956821,0.04864152,0.00012721,-0.00838405,0.07019059,-0.05825652,-0.04962061,-0.02105073,-0.09497847,0.00953329,0.0934234,-0.00877478,-0.27259299,-0.0019841,-0.00010528,0.00092034,0.04556349,0.01552157,0.0651544,-0.04878431,-0.04264767,0.03824001,-0.05995236,0.06314716,-0.00641007,-0.05607524,0.01174295,-0.00622507,0.04101006,0.03011033,0.08087381,-0.05829632,0.01607181,0.08886533,0.21173242,0.04754106,0.00706606,0.04340298,0.00404799,0.0928656,0.03263361,0.01839728,-0.00159533,-0.05280515,0.06823917,0.00658982,0.01596972,0.01390558,-0.02237732,-0.02100738,0.04340271,0.01085486,-0.04276317,0.05177785,-0.03932125,0.04596315,0.08098362,0.0003651,-0.03126229,-0.03359874,0.01497547,0.00184759,0.01180836,-0.00163769,0.00806629,-0.01938671,0.01767012,0.0412135,0.00532688,-0.04237905,-0.06649332,0.0036929,0.0195927,0.03014089,0.04507843,0.05906768,0.01544478],"last_embed":{"hash":"59b402b603a02ab09f08ebb3d0f20d07db4508e22015da8743e532572230b90a","tokens":410}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.06383429,0.04537486,-0.00678327,0.00125788,-0.00580676,-0.01901961,0.07449856,-0.02916064,0.01067922,-0.0146783,-0.00146774,-0.02277528,-0.02031565,-0.00825777,-0.05855209,0.04575235,0.00825886,0.0444198,0.04737029,-0.00778642,0.00692275,0.02258787,0.05342903,0.05366869,-0.01311124,-0.01844685,0.00579439,-0.05325744,0.01078833,0.04143485,0.04727944,-0.05625413,0.00376605,-0.02351257,-0.06615897,0.01858215,0.02993876,-0.02366519,-0.04020903,0.02262624,-0.01095759,-0.02082106,-0.04666923,0.01007752,-0.1045737,-0.01860261,0.04354266,-0.01946681,0.00051044,0.01086964,0.01057481,-0.05174249,-0.00447111,0.04521802,0.00653571,0.03073506,-0.03478368,-0.04682375,-0.01659263,-0.00294461,0.01367992,0.04676076,0.07987431,0.01788919,-0.01936861,0.02785863,0.00237864,-0.04458931,0.00123079,-0.01294873,-0.08923823,0.00829877,-0.00744962,0.01856971,0.07016548,-0.03864793,0.0512988,0.00717206,-0.02708644,0.00390315,-0.10702439,0.02382656,-0.00540823,0.04322337,0.03326369,-0.01771878,0.00657728,-0.03034433,0.0395746,0.00339991,0.03490086,0.03551208,0.00122353,0.00513135,0.01354554,-0.07697223,-0.01053537,0.01521866,-0.01160359,-0.0442379,0.0424741,-0.00826448,-0.05098327,-0.01130343,-0.02763559,-0.02088609,0.003158,0.00565592,-0.03330149,-0.00339079,-0.03290503,0.0201,-0.02516446,-0.04858959,-0.08143965,0.03431884,-0.03773126,0.00343413,0.03768906,0.08590557,0.03705208,0.01853135,0.0126287,-0.01484976,-0.0462775,0.00884163,0.00464719,-0.02556306,0.02006271,-0.02968455,-0.01014714,0.00125847,-0.00907931,-0.01731932,-0.01128565,-0.00405758,0.02071997,-0.05264051,0.01786143,-0.03784107,0.03736767,-0.02482439,-0.00493748,-0.0166779,-0.01889956,0.05834084,-0.04149953,-0.05644463,-0.01472861,0.04251026,-0.02359317,-0.0041075,-0.0452735,-0.05254715,0.01944091,0.02086583,0.0322822,0.06559132,0.00933875,-0.00337572,-0.00640054,-0.00328647,0.04704392,0.036047,0.0347277,0.02813948,0.04723056,0.00849545,-0.05945017,-0.00041154,-0.01839285,0.01574331,0.02707038,0.04613394,0.0581153,-0.04594122,0.06914493,0.01806027,-0.05252463,-0.03167415,-0.0083378,-0.03624746,0.00702322,0.0358944,0.01658167,0.02549986,-0.00601871,0.03300253,0.01537167,-0.00472282,0.01971938,-0.00760545,0.0087089,0.00127737,0.05008325,0.01465822,-0.01411953,-0.05236582,-0.02399343,0.02078606,0.02619981,-0.0243276,-0.05329138,-0.04139029,0.04938371,0.01707256,-0.02444063,-0.0173057,-0.01909611,0.02893539,0.05759287,0.01429325,0.02411461,-0.00784329,-0.05858573,0.06070986,-0.06311148,0.09602643,0.01886793,-0.0136369,-0.00681237,-0.1082973,-0.0325588,-0.03020811,0.02113836,-0.06302647,-0.03031398,0.03466285,0.0344496,0.01008982,-0.00898907,0.05406021,-0.02158889,-0.01548349,-0.03630018,-0.00508688,-0.01081614,0.03889184,0.03587064,0.02848458,-0.00923035,-0.00020655,0.00286396,-0.03084283,0.00409552,-0.00241017,-0.0662079,0.0052251,-0.00646996,0.00146621,0.02576336,-0.00822247,0.00647962,0.09016725,0.00928068,-0.03006214,-0.0289761,-0.04685223,0.01207962,-0.02293634,0.0062818,0.04896463,-0.04771726,0.01573032,-0.02582283,-0.03533536,-0.01514525,0.01996413,-0.05660804,0.08055504,-0.03306482,-0.04637984,0.07646023,0.01957966,-0.005414,-0.0034076,-0.06902998,-0.01959863,-0.04573485,-0.03621812,-0.04372697,-0.05062618,0.01860477,0.02752466,-0.07397579,0.03064821,0.00410921,0.0131178,0.05555682,-0.03621111,0.04304884,-0.00336961,-0.05028974,0.04581473,-0.04649062,-0.04086069,0.01743647,0.0385741,0.00453175,-0.05872653,-0.02819139,0.07261318,0.01725761,-0.0278866,0.0087519,0.01511896,0.00438031,0.0153705,-0.01167189,-0.0366062,0.01652249,-0.05029675,0.02285983,0.0159522,0.04189384,0.06343708,0.00449585,-0.01129514,-0.01131399,-0.00379149,-0.04479982,-0.01037863,0.02372965,0.02494011,0.03149789,-0.06375959,0.03944242,-0.00075429,0.02343367,0.04017041,-0.04561311,0.00549574,0.00365827,-0.01235804,0.03385216,-0.01057423,-0.02555863,-0.06814633,0.02813225,-0.0584848,0.0269265,-0.05826256,0.01026581,-0.04147849,-0.01752693,0.01029708,0.00257877,0.02410649,-0.05267528,-0.04276232,-0.01229643,-0.03131955,-0.04415305,0.00275168,0.04048843,-0.0207773,-0.01671316,0.01225006,0.01462551,0.01094926,-0.00465063,-0.04326303,-0.05422603,-0.05213715,-0.04315579,0.02916579,0.08038951,0.03367899,-0.06593248,-0.02222531,0.0288453,0.02533814,-0.0455042,-0.00824455,0.05120368,-0.00846196,0.01220138,-0.0011848,0.0317282,-0.01768681,0.01260712,-0.02708264,-0.02764937,-0.02796922,-0.1082244,-0.04053737,0.04218012,-0.04152314,-0.02698733,-0.05275648,-0.06179675,0.01308599,-0.04279248,0.0502181,0.01007311,0.01038617,0.04733428,-0.02052441,-0.02337616,-0.013337,0.02870751,0.03347923,0.00234182,0.0155189,-0.01006173,0.06226338,-0.0194679,0.06801362,-0.0197925,-0.02895864,0.00232145,0.04449383,-0.00762067,-0.00283645,0.0099974,0.02704902,-0.01514632,-0.03888796,0.02292273,0.05803626,0.01466346,-0.04316358,0.04053292,0.03746004,0.02855438,-0.0312193,-0.06409596,0.03348916,0.00354004,0.01352032,-0.02114595,0.02891782,-0.09531904,-0.0614101,-0.02813923,-0.02600897,0.08475368,0.02881219,0.01130145,0.01628313,-0.03574874,-0.01887334,-0.01058509,-0.03634458,0.06855742,0.05515296,-0.01274371,-0.02332108,-0.00238749,0.07503348,-0.01011685,-0.04279277,-0.06638024,-0.03637231,0.03419951,0.01512395,0.02000431,-0.04773981,-0.01386196,-0.00624908,-0.06459857,-0.02031661,0.04875203,-0.01130122,0.01722641,-0.00883676,0.01260774,-0.03351503,-0.05806015,0.00000582,0.00884117,-0.02802269,-0.00170385,-0.01551675,0.08056215,0.03664528,-0.03079687,-0.01002141,-0.06237046,-0.02701854,-0.00885426,0.04046333,0.02334624,0.01502761,-0.00627957,0.05121367,-0.02926062,-0.00331698,0.01367389,-0.01782833,-0.0289199,0.07362521,0.04037573,0.00992276,0.01693219,-0.01093109,0.01282365,0.05197503,-0.0037298,0.04631373,0.01248683,0.04968121,-0.03253272,-0.00794377,0.05476902,0.04322629,-0.03972765,0.01905155,0.06182313,-0.02279073,-0.0239633,-0.08843303,0.08091296,0.0243528,-0.02813803,-0.05533443,-0.03160403,0.02488623,-0.02884126,0.00619133,0.00224667,0.01636133,-0.06744885,-0.00157979,0.0374563,-0.02164109,-0.0303858,0.07566209,0.04805396,0.03641011,-0.03815013,0.00022287,0.01053731,-0.01737146,0.03153645,-0.04660669,0.04963924,-0.00896178,-0.01676884,0.05065184,0.09178942,0.02781933,0.0093857,0.02116535,-0.02304967,-0.00036524,0.03167745,-0.00933334,0.0634032,0.00517076,0.00021766,0.04872344,0.02716522,-0.01265641,-0.01968911,0.01745578,0.01193419,-0.03060278,-0.03841199,0.05722049,0.00391569,0.04429422,-0.03065792,0.01901426,-0.00438217,-0.00595183,-0.0486226,0.01720248,0.01187035,0.03296534,-0.04616084,-0.00221912,-0.00736146,0.02904395,-0.06176984,-0.02618139,0.00799543,0.00222206,0.05226148,-0.08374602,0.02838264,-0.01292009,0.0538209,-0.00897842,-0.01405313,0.01220214,0.00989428,0.05098023,0.01771318,0.00703264,-0.00325938,-0.05183677,-0.0256983,-0.01829835,-0.02135431,0.04475721,-0.00978012,0.02361716,0.03514566,-0.03862298,0.01225053,-0.02728861,-0.00989768,-0.00076341,0.00221872,-0.08123224,-0.0269461,-0.07292279,-0.00137324,0.03923481,0.00179049,0.05582665,0.02003729,0.01971594,0.00933874,-0.05062337,-0.00268568,-0.01366203,-0.00840593,-0.04095765,-0.0049109,-0.04448497,-0.02737814,-0.04240055,-0.02059075,0.01231009,0.01150365,0.00469331,0.0524857,-0.05622553,-0.06924055,-0.02596866,0.04398196,0.02910957,-0.00897418,0.03799327,-0.07838728,-0.05381851,-0.03908759,-0.03837175,0.01149461,0.02510838,0.00455779,0.04955727,0.01979543,-0.02299243,-0.00974193,0.00690699,0.03184395,-0.02349779,-0.00318958,-0.01047604,-0.01314198,-0.01013892,-0.04650513,0.04514224,-0.03447508,0.01953012,-0.00246872,0.04516698,-0.0517557,-0.02177794,-0.00908784,-0.02726282,-0.05825712,0.05505252,0.03910954,0.01821785,-0.0349356,-0.00190231,-0.09349077,0.00389688,-0.02000664,0.01189911,0.01755699,0.04015215,0.02271315,0.03181181,0.01578549,-0.02916392,-0.09542488,0.05524086,-0.00855039,-0.01624698,0.05411326,-0.01961778,0.02460086,0.01870694,-0.04678892,-0.00248202,-0.02662402,0.02367297,-0.01885799,-0.01401633,0.06711594,-0.04587944,0.06360637,-0.03792476,0.02148128,0.04094121,-0.01736198,0.0258382,-0.02761413,0.02061764,-0.0207151,0.00241296,0.02154309,0.00944979,0.10942987,-0.0439827,-0.06463076,-0.00626302,-0.02028777,0.03557714,0.01032982,-0.03621005,-0.00489846,0.02810644,-0.04522432,-0.00179067,-0.03629046,-0.051854,0.06355766,0.03090702,-0.00784749,-0.00949728,-0.02527255,-0.00594411,0.00816494,-0.00506895,-0.04231322,-0.09830385,0.00287073,0.05127282,0.03962668,0.00727975,-0.03730368,-0.00482021,0.04894612,-0.01040311,-0.0092984,0.00649252,-0.03176745,0.00509159,-0.00569395,0.05877437,-0.01124617,0.00634439,0.00094966,0.03327291,0.00108347,0.03216354,-0.01127134,0.05866431,0.02112348,0.02840799,-0.04976097,-0.0256695,0.00000114,-0.00773032,-0.02930156,0.02412826,-0.00230821,-0.01995166,-0.01220732,-0.04643224,-0.05369934,0.04388007],"last_embed":{"tokens":1292,"hash":"1glvme0"}}},"last_read":{"hash":"1glvme0","at":1751268683011},"class_name":"SmartSource","outlinks":[{"title":"Jenkins","target":"Jenkins","line":11},{"title":"反序列化","target":"反序列化","line":12},{"title":"反序列化","target":"反序列化","line":12},{"title":"反序列化","target":"反序列化","line":13},{"title":"session","target":"session","line":30},{"title":"token","target":"token","line":30},{"title":"反序列化","target":"反序列化","line":31},{"title":"序列化","target":"序列化","line":31},{"title":"json","target":"json","line":32},{"title":"序列化","target":"序列化","line":33},{"title":"json","target":"json","line":34},{"title":"序列化","target":"序列化","line":36},{"title":"Java","target":"Java","line":39},{"title":"反序列化","target":"反序列化","line":44},{"title":"序列化","target":"序列化","line":45},{"title":"反序列化","target":"反序列化","line":48},{"title":"PHP","target":"PHP","line":56},{"title":"Python","target":"Python","line":56},{"title":"序列化","target":"序列化","line":60},{"title":"反序列化","target":"反序列化","line":61},{"title":"#Python的反序列化实验","target":"#Python的反序列化实验","line":62},{"title":"反射（reflection）机制","target":"https://xz.aliyun.com/t/7029/","line":64},{"title":"Java","target":"Java","line":64},{"title":"变量","target":"变量","line":67},{"title":"Java","target":"Java","line":69},{"title":"序列化","target":"序列化","line":95},{"title":"序列化","target":"序列化","line":125}],"metadata":{"tags":["网络安全/漏洞/Web安全"],"英文":"Deserialization Vulnerability","aliases":["Deserialization Vulnerability"],"cssclasses":["editor-full"]},"blocks":{"#---frontmatter---":[1,9],"#简介":[10,15],"#简介#{1}":[11,15],"#相关漏洞":[16,21],"#相关漏洞#{1}":[17,17],"#相关漏洞#{2}":[18,20],"#相关漏洞#{3}":[21,21],"#常用工具":[22,26],"#常用工具#{1}":[23,23],"#常用工具#{2}":[24,24],"#常用工具#{3}":[25,25],"#常用工具#{4}":[26,26],"#漏洞环境":[27,73],"#漏洞环境#可能出现的位置":[29,41],"#漏洞环境#可能出现的位置#{1}":[30,30],"#漏洞环境#可能出现的位置#{2}":[31,32],"#漏洞环境#可能出现的位置#{3}":[33,34],"#漏洞环境#可能出现的位置#{4}":[35,35],"#漏洞环境#可能出现的位置#{5}":[36,36],"#漏洞环境#可能出现的位置#{6}":[37,38],"#漏洞环境#可能出现的位置#{7}":[39,40],"#漏洞环境#可能出现的位置#{8}":[41,41],"#漏洞环境#漏洞成因/环节":[42,54],"#漏洞环境#漏洞成因/环节#{1}":[43,49],"#漏洞环境#漏洞成因/环节#{2}":[50,50],"#漏洞环境#漏洞成因/环节#{3}":[51,53],"#漏洞环境#漏洞成因/环节#{4}":[54,54],"#漏洞环境#漏洞原理":[55,73],"#漏洞环境#漏洞原理#{1}":[56,61],"#漏洞环境#漏洞原理#{2}":[62,62],"#漏洞环境#漏洞原理#{3}":[63,63],"#漏洞环境#漏洞原理#{4}":[64,71],"#漏洞环境#漏洞原理#{5}":[72,73],"#实际测试":[74,137],"#实际测试#Python的反序列化实验":[76,137],"#实际测试#Python的反序列化实验#序列化与反序列化":[78,100],"#实际测试#Python的反序列化实验#序列化与反序列化#{1}":[79,94],"#实际测试#Python的反序列化实验#序列化与反序列化#{2}":[95,96],"#实际测试#Python的反序列化实验#序列化与反序列化#{3}":[97,99],"#实际测试#Python的反序列化实验#序列化与反序列化#{4}":[100,100],"#实际测试#Python的反序列化实验#系统命令序列化传输":[101,137],"#实际测试#Python的反序列化实验#系统命令序列化传输#{1}":[102,116],"#实际测试#Python的反序列化实验#系统命令序列化传输#{2}":[117,117],"#实际测试#Python的反序列化实验#系统命令序列化传输#{3}":[118,123],"#实际测试#Python的反序列化实验#系统命令序列化传输#{4}":[124,125],"#实际测试#Python的反序列化实验#系统命令序列化传输#{5}":[126,134],"#实际测试#Python的反序列化实验#系统命令序列化传输#{6}":[135,137]},"last_import":{"mtime":1744268861891,"size":4991,"at":1748488129023,"hash":"1glvme0"},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md","last_embed":{"hash":"1glvme0","at":1751268683011}},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#漏洞环境": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10995845,0.0079889,-0.02083085,-0.05018989,0.00033972,-0.00642758,-0.00978999,0.03483862,0.05883764,-0.01276699,-0.01919137,-0.06843989,0.05888071,0.07251744,0.08276035,0.00262276,-0.00909353,-0.00275444,-0.05977177,0.04160265,0.0596634,-0.04907491,-0.02364839,-0.0972492,0.01677217,0.04108405,0.01712863,0.01026264,-0.0157909,-0.14647749,0.04354749,-0.02965206,0.04716224,0.0314564,-0.03306463,0.02842855,0.01640113,0.0686673,-0.02604955,0.01279807,0.0264415,0.04963169,0.02830876,-0.03213567,-0.04048312,-0.05916379,-0.04819292,-0.02127755,0.02512042,-0.03855259,-0.02903498,-0.03291655,-0.00331166,-0.03415579,-0.02419728,0.01416994,-0.02228725,0.02744493,-0.0110898,0.01666211,0.01936741,0.04776754,-0.21226972,0.03126039,0.01004246,-0.04807448,-0.03161388,0.00782204,0.0127434,0.04217713,-0.07253152,0.04536923,-0.00310743,0.04608029,0.03625989,-0.03575948,0.03684586,-0.01133437,0.0350723,-0.02130573,-0.07281724,0.04168937,-0.01613297,-0.04025098,-0.01396628,0.00675432,-0.03080862,0.01112518,0.01088309,0.00861638,0.00202387,-0.02985915,-0.04230711,0.04539984,-0.01306996,0.07296848,-0.01830291,0.04691009,-0.08240283,0.11005343,-0.04687743,0.03111715,0.02532533,-0.05003958,-0.01185923,-0.06925363,-0.00580466,-0.04712674,0.02063483,-0.03268091,-0.03449149,-0.02683915,0.04677833,-0.00362836,0.00709317,0.02378221,0.04943473,0.00045555,-0.03033093,0.02653774,-0.00061598,-0.00363148,0.05504035,-0.02361865,-0.01500814,-0.04768919,0.0058293,0.08273528,0.02055082,0.01817027,0.04379148,-0.0355965,-0.0485974,-0.03071867,-0.02873565,0.0069541,-0.01972415,-0.02651592,0.02315762,-0.03327226,-0.00297671,-0.06813335,-0.0210274,-0.13105221,-0.07794552,0.0463711,-0.01354865,-0.02913386,0.04044794,-0.02858812,-0.04020004,0.08211987,-0.04233236,-0.00837488,-0.0002493,-0.0095953,0.07261367,0.10968019,-0.01085041,-0.01619113,-0.00502881,0.01688137,-0.07507674,0.19666083,-0.00755333,-0.05685386,0.0014817,-0.00755995,-0.00671618,-0.03849201,-0.00285463,0.05993333,0.00614806,0.01011837,0.11000721,-0.0383432,-0.02606698,-0.04545308,0.02758071,0.02839386,0.06263794,-0.05084177,-0.06323906,0.03838015,0.01975518,-0.04052105,0.00020483,-0.01105295,0.01797399,-0.07505654,-0.10134626,-0.0023192,-0.03203921,-0.00101632,-0.05125892,-0.10232852,0.02103313,0.02964364,0.01985341,-0.04373395,0.11128453,0.03921328,-0.00304094,0.03471,-0.01474317,-0.01616198,0.02536405,0.02443084,0.01833201,0.02686583,-0.01226554,0.00031117,-0.03785452,0.01711637,-0.03502582,0.02579543,0.01192543,0.00736004,0.03457778,0.06654798,-0.01973577,-0.04753825,-0.07914579,-0.20929536,-0.04333357,0.01690432,0.00360937,0.01061627,-0.03088304,0.03453933,-0.03256989,0.10570179,0.07011779,0.06108699,0.01750213,-0.10075589,-0.04045022,0.00189928,0.01508524,-0.01023232,-0.00719362,-0.01650452,-0.00492394,0.00066635,0.04465281,0.01268574,-0.03492179,0.02469307,-0.01882306,0.1461641,0.01275211,0.01776167,0.03117716,0.01465463,-0.00153161,0.00671451,-0.07486326,0.02435132,0.04178093,-0.05071156,0.02038205,0.00130503,-0.00481488,-0.00310955,0.04528026,-0.04739337,-0.10622103,-0.03851817,-0.04767808,-0.02243783,-0.02685643,0.0443125,0.02419466,-0.00513832,0.01701074,0.01329562,0.01032058,-0.03568858,-0.04672709,-0.02748943,-0.03315697,-0.0112458,0.03591501,0.03151494,-0.0171683,0.00787737,0.02743548,-0.00609114,-0.03978833,0.00793643,0.01262407,-0.07606735,0.02246725,-0.03982674,0.14941007,-0.00641604,-0.00695909,0.0792285,0.01081011,0.00374477,-0.06340342,0.0175591,0.01995635,0.01727797,0.05357657,0.0562892,0.01373254,-0.02659314,0.06306267,0.00894106,-0.01633453,0.04168355,-0.0723896,-0.07100412,-0.01471223,-0.06365324,0.05182012,0.02831493,-0.00983275,-0.28438172,0.05838399,-0.04694035,0.01953728,0.01250998,0.04306149,0.06684709,0.00582333,-0.0564496,0.07264587,-0.03380464,0.07029112,0.04325472,-0.04602242,0.01669914,-0.02223749,0.03857242,-0.00102829,0.0928001,-0.02928616,0.04709427,0.07470389,0.2080362,0.00574732,0.06133367,0.05772344,0.0247403,0.05638688,0.12071978,0.02410995,0.02715715,-0.0413061,0.06682468,-0.00828611,0.06151037,-0.02215163,-0.04689492,-0.03971923,0.04174967,-0.01228125,-0.03246098,0.05553808,-0.06361125,0.00047848,0.09445918,0.04970344,-0.02797167,-0.07040845,-0.03494089,0.04089426,-0.00850503,-0.03287047,-0.0200361,-0.01231236,0.02518153,0.05090346,0.01881023,-0.00329873,-0.04704174,-0.02757176,-0.00507191,0.00243452,0.10481202,0.09917833,0.01688338],"last_embed":{"hash":"556afe35668dfaafac0c00f5d9cf4e12736f2e448bba8ef9696417a97aebd2dd","tokens":446}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.06376362,0.02077562,0.00422766,0.02608516,-0.00047548,0.01513596,0.08165633,-0.0199379,0.00396988,-0.01053045,-0.00636519,-0.00139514,-0.04000593,-0.00041661,-0.03650702,0.03464567,0.01282089,0.04807814,0.0252254,-0.01687986,-0.00455656,0.02642992,0.06539824,0.04551161,0.01813468,-0.01252865,0.0076878,-0.02478537,0.03006013,0.04901445,0.06467288,-0.04601555,0.00954499,-0.02634314,-0.07576659,0.02129589,0.03069973,-0.02529792,-0.0411557,0.03228303,-0.04170913,-0.00706847,-0.0293371,0.01232034,-0.10522469,0.00415432,0.02810202,-0.02617215,-0.00039395,0.01808935,-0.0010126,-0.057369,0.00247804,0.0283817,-0.00102657,0.05749948,-0.02840317,-0.04217803,-0.01970263,0.00857142,0.01382389,0.01124246,0.08280931,0.01612842,-0.00188255,0.02652741,0.0225065,-0.04620054,-0.00689258,-0.01425669,-0.09800978,-0.00007812,0.00419283,-0.00463935,0.05590674,-0.06222669,0.06306685,0.00944408,-0.01038675,0.00919188,-0.1098557,0.02281304,0.00282237,0.07379592,0.0381033,-0.04220989,0.0192792,-0.02508399,0.01472715,0.00920503,0.03213469,0.03873191,-0.00794118,-0.00769793,0.02727123,-0.07644589,-0.01336885,0.00848898,-0.02001481,-0.05620305,0.06099461,-0.00294659,-0.05939754,-0.0079219,-0.04123159,-0.05047306,-0.03467428,0.02182677,-0.04371959,-0.00876976,-0.01454075,-0.00296378,-0.02368531,-0.04132728,-0.07429396,0.05785897,-0.03700328,-0.01245421,0.04014263,0.10422485,0.05730532,0.02746622,0.0156888,-0.02707489,-0.04054163,0.00890826,-0.00633878,-0.03768154,0.04521333,-0.00822857,0.00699274,0.01610749,-0.00677542,-0.00629297,-0.00960962,0.0034093,0.02535209,-0.03828543,0.04517417,-0.03636146,0.02529976,-0.02453333,-0.00853878,0.0034698,0.01837182,0.0630246,-0.01911187,-0.04185898,-0.03745485,0.05530689,-0.01596665,0.00087737,-0.03338813,-0.05534051,0.00542459,0.02102515,0.02993286,0.04653681,0.00805758,0.0195309,-0.01798482,-0.0288719,0.0424573,0.04500455,0.02096015,0.00289922,0.04764761,0.01795917,-0.05601628,-0.0088181,-0.02397523,0.02845608,0.03504999,0.04532156,0.06169411,-0.02466976,0.03656732,0.04986639,-0.03740671,-0.01854154,-0.0056514,-0.04831863,0.01303642,0.02965447,0.02170602,0.01855437,-0.03887035,0.01547823,0.03872022,-0.01340865,0.01781694,-0.01061654,0.00890181,0.00521858,0.02837448,0.0189146,0.00192166,-0.03792648,-0.02981932,0.03217027,-0.01608461,-0.0345817,-0.01531376,-0.04841569,0.0215257,0.01912457,-0.02724425,-0.02221476,-0.00711541,0.0575754,0.03618857,0.01757307,0.02434345,-0.00061652,-0.06074525,0.03892886,-0.05302791,0.07385218,0.02662753,0.00640565,-0.00111369,-0.08687719,-0.06244955,-0.0120064,0.01106546,-0.05951106,-0.01106361,0.02522874,0.04661868,-0.00090701,0.00717376,0.07953031,-0.03781378,-0.01464409,-0.05317456,0.00302992,-0.0072671,0.04213662,0.02884899,0.0119263,-0.00023296,0.00861463,-0.00715794,-0.01283456,-0.00560014,0.00226604,-0.04638344,0.00196402,-0.00668037,0.03925996,0.0134315,0.00342131,-0.01498375,0.09662224,0.01138441,-0.0474818,-0.04713357,-0.02466418,0.00438972,-0.03277594,0.01915679,0.02882289,-0.04120928,0.03055432,-0.00760051,-0.04025201,-0.03339694,0.01315364,-0.05798835,0.06456579,-0.05261398,-0.02995579,0.07040258,0.00528291,-0.00911751,0.01550028,-0.07062571,-0.01424919,-0.0457382,-0.04392846,-0.0290142,-0.0282379,0.01337275,0.0178039,-0.05342397,0.04497339,0.00814147,0.00913166,0.01693647,-0.03707433,0.03003722,0.00083823,-0.03792163,0.06019957,-0.04857438,-0.04069526,0.01215041,0.03660049,0.00273745,-0.06516274,-0.03352867,0.06543688,0.02332686,-0.0087775,0.00616582,0.01637959,0.00417129,0.01201724,-0.02408904,-0.06154434,0.00055821,-0.03929446,0.03030241,0.00393996,0.03386598,0.06944986,0.00253536,0.00777689,-0.0182258,0.00047218,-0.05043171,0.00628162,0.0217309,0.00660892,0.03370402,-0.07365642,0.05876951,0.01265045,0.05633403,0.02570174,-0.04647009,0.01927987,0.00244098,0.00147921,0.04072315,-0.01531534,-0.03811477,-0.06887189,0.01824956,-0.05652646,0.05150991,-0.05483937,0.01819379,-0.05949067,-0.00524845,0.01896662,-0.00819059,0.01507304,-0.07487243,-0.03817477,0.01714328,-0.03362209,-0.03091922,0.0005688,0.04241266,-0.0169609,-0.01245669,-0.00504449,-0.01853663,0.02127095,0.0042189,-0.05226627,-0.05924525,0.00032773,-0.05111646,0.02767575,0.06846243,0.06642769,-0.05736531,-0.03648413,0.01268798,0.01032611,-0.04248467,-0.00831904,0.02958055,-0.01183292,-0.02302895,0.00752125,-0.00061431,-0.02580372,0.01288669,-0.02285957,-0.02623455,-0.03454614,-0.10578378,-0.03493547,0.02655882,-0.02035279,-0.05755808,-0.06226787,-0.04147713,0.02953563,-0.07667202,0.04194023,0.02389079,-0.00241375,0.0406097,-0.01797034,-0.03181865,-0.01284955,0.03102037,-0.00946871,0.02020731,-0.00026227,-0.02704746,0.05297167,-0.04280069,0.07352336,-0.01762218,-0.04430496,-0.01596378,0.04602077,0.00652138,-0.0006215,0.01723808,0.01430378,0.01096816,-0.02393226,0.01402217,0.06243485,0.02316119,-0.02475297,0.05189594,0.03535866,0.03756461,-0.01345664,-0.05353286,0.03203795,0.01237954,-0.0207268,-0.0184555,0.02795966,-0.08752596,-0.05170007,-0.02451675,-0.04407726,0.06637708,0.00061975,0.02185418,0.01588111,-0.03965376,-0.03215231,0.0066632,-0.03229165,0.06424917,0.05298189,-0.0051643,-0.04054069,-0.01195319,0.07266133,0.00580875,-0.02934583,-0.06774871,-0.04089239,0.02149175,-0.01241401,0.01398263,-0.02284762,-0.02686751,-0.01996122,-0.05985831,-0.01122165,0.04702321,-0.0055542,0.01163401,-0.01352474,0.0034601,-0.04124911,-0.06670015,-0.01102857,0.02115376,-0.01548401,0.00988697,-0.01731484,0.07494864,0.02436096,-0.00414745,0.00284232,-0.0757598,-0.03633517,0.01784033,0.05847768,0.02038811,-0.00290841,-0.00176618,0.070811,-0.03650333,-0.01154366,0.02048302,-0.02208393,-0.03703834,0.05574051,0.03910228,-0.02206121,0.02124455,-0.00406403,-0.01058846,0.05964651,-0.00295137,0.04413403,-0.00623868,0.06461956,-0.04669807,0.00332082,0.04139118,0.04009793,-0.03956867,0.03466525,0.07370172,-0.034329,-0.02079751,-0.0745534,0.07868232,0.02311423,-0.03382259,-0.06981853,-0.03085266,0.01129548,-0.00671444,-0.0195244,-0.00618942,0.02923631,-0.04013291,-0.00485881,0.02287405,-0.04689375,-0.01778391,0.05336847,0.05509846,0.00427703,-0.04784269,-0.01958057,0.00413891,-0.0099569,0.04031319,-0.03674874,0.04411706,-0.02120215,-0.01825261,0.06060373,0.09024218,0.00689429,0.00523516,0.04467705,-0.03547042,-0.00243859,0.01538264,-0.02092005,0.06458213,-0.00246607,0.01507911,0.05175143,0.02983771,-0.0182332,-0.03566693,0.01406092,0.03530879,-0.05530221,-0.01800809,0.06784207,-0.00919684,0.03384807,-0.03422489,0.00248529,-0.00338603,0.01915727,-0.04439402,0.01549568,0.01388654,0.03764148,-0.05086941,-0.02751376,0.01823426,0.01767928,-0.05433879,-0.00082079,0.00248551,-0.01058764,0.04762992,-0.07726587,0.02596786,0.00890384,0.04107072,-0.00496156,-0.02789658,0.00699846,0.00306335,0.03339757,0.01907022,0.00572433,0.01979605,-0.06717236,-0.01262003,-0.00761672,-0.04245835,0.00501307,-0.03071042,0.04252262,0.04725803,-0.03621393,0.00229119,-0.01301293,0.00144676,0.00113478,0.01309399,-0.0976219,-0.01558977,-0.06449481,0.01167037,0.04144477,0.00704632,0.01926598,-0.0069769,0.00143519,0.01341259,-0.04821637,-0.00300403,-0.00163658,-0.00925065,-0.02849421,-0.0254738,-0.04145866,-0.03456722,-0.02035587,0.00263645,0.04558207,0.00968389,0.01658318,0.05495174,-0.07412603,-0.06354,-0.03210404,0.04040895,0.04264896,-0.00317495,0.04981719,-0.08153675,-0.05112078,-0.04013664,-0.03184013,0.02914724,0.0304558,0.00320199,0.06422978,0.01195765,-0.02938364,-0.0214023,0.00240123,0.03386154,-0.03715409,-0.00907973,-0.01625123,-0.00490563,-0.01595264,-0.05475486,0.03668942,-0.04247487,0.02118064,-0.00061638,0.05608337,-0.05489246,-0.04158416,0.00920173,-0.0238115,-0.02419034,0.04549786,0.02891804,0.02899915,-0.03117631,0.00837889,-0.06640932,-0.01245788,-0.01596447,0.01886885,0.02200792,0.03791058,0.0300122,0.04023037,0.01726962,-0.02585355,-0.08923681,0.05253405,-0.00228754,-0.00126013,0.07231367,-0.02224141,0.02035702,0.00776367,-0.03136785,-0.01297837,-0.00851594,0.00683339,0.00753456,-0.0015403,0.05458991,-0.02950314,0.05466824,-0.0133866,0.00783645,0.03950612,-0.01355969,0.02498224,-0.02516125,0.00471936,-0.04101602,0.00012656,0.01148283,-0.00134841,0.08818217,-0.05607127,-0.04762898,0.02260884,-0.02546622,0.01021168,0.01633367,-0.06598831,-0.01755604,0.05454076,-0.00947863,-0.00194768,-0.03981453,-0.03328963,0.0354571,0.0294335,-0.04980187,-0.0118762,-0.02379126,0.01300012,0.00771138,0.0064708,-0.04531746,-0.08302809,0.0098839,0.05696169,0.03721444,-0.01022854,-0.03103698,-0.00910451,0.04684797,-0.00889498,0.00211246,-0.00040171,-0.02893887,-0.02902593,-0.00919103,0.02033959,-0.0014843,0.00620191,-0.01777763,0.05288669,-0.01882475,0.0536239,-0.01167583,0.05552334,0.03795889,0.02483297,-0.03829907,-0.00394735,0.00000103,0.01795202,-0.04424895,0.01316198,-0.00354522,-0.00633343,-0.01113588,-0.04588915,-0.05699421,0.03700397],"last_embed":{"hash":"1u635ni","tokens":534}}},"text":null,"length":0,"last_read":{"hash":"1u635ni","at":1748397845201},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#漏洞环境","lines":[27,73],"size":1003,"outlinks":[{"title":"session","target":"session","line":4},{"title":"token","target":"token","line":4},{"title":"反序列化","target":"反序列化","line":5},{"title":"序列化","target":"序列化","line":5},{"title":"json","target":"json","line":6},{"title":"序列化","target":"序列化","line":7},{"title":"json","target":"json","line":8},{"title":"序列化","target":"序列化","line":10},{"title":"Java","target":"Java","line":13},{"title":"反序列化","target":"反序列化","line":18},{"title":"序列化","target":"序列化","line":19},{"title":"反序列化","target":"反序列化","line":22},{"title":"PHP","target":"PHP","line":30},{"title":"Python","target":"Python","line":30},{"title":"序列化","target":"序列化","line":34},{"title":"反序列化","target":"反序列化","line":35},{"title":"#Python的反序列化实验","target":"#Python的反序列化实验","line":36},{"title":"反射（reflection）机制","target":"https://xz.aliyun.com/t/7029/","line":38},{"title":"Java","target":"Java","line":38},{"title":"变量","target":"变量","line":41},{"title":"Java","target":"Java","line":43}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#实际测试": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.1279746,-0.00297514,-0.0183693,-0.04452879,-0.02408537,-0.00081637,-0.01141344,0.01414352,0.06994701,0.0146114,-0.00187853,-0.11938854,0.04043315,0.04482961,0.06475227,-0.0196387,-0.03032014,-0.00561186,-0.06301288,0.00276081,0.09585448,-0.03666032,-0.0293736,-0.08780923,-0.00167013,0.04422989,0.01761447,0.01357015,-0.03081819,-0.1671772,0.06471545,-0.00699729,0.02295977,-0.01406754,0.01471807,0.02125674,0.03970198,0.03592523,-0.0060842,0.05172803,0.03364491,0.0274208,0.03213795,-0.04475179,-0.02700206,-0.06398624,-0.08604752,-0.00606109,0.01336219,-0.03533489,-0.0209174,-0.01454995,-0.05814083,-0.01963221,-0.01622783,0.00118307,0.01428055,0.00765531,0.03879074,-0.01517486,0.01965017,0.00659488,-0.19059816,0.08123717,0.01340834,-0.02656582,-0.02944304,0.00563294,0.02682988,0.02219702,-0.09930836,0.02537848,0.01486411,0.06895315,0.0340086,-0.03992632,0.04013772,-0.02743969,-0.03425873,-0.04826138,-0.04864797,0.04867585,-0.03837845,-0.00578212,-0.00260464,0.01238516,-0.00001156,-0.03634396,0.02987393,-0.04208864,0.03780846,-0.07716142,-0.04285356,0.05512139,-0.02991651,0.02783263,0.03099666,0.04157086,-0.102902,0.11830176,-0.05144145,0.01853146,-0.01726035,-0.02469106,-0.00900463,-0.03908065,-0.02070027,-0.03308557,-0.05616552,-0.03670022,-0.00830517,-0.06362309,0.07431564,-0.01777285,0.00030845,0.04253307,-0.00516579,-0.02310571,-0.03947207,0.00892221,0.0164685,-0.0230355,0.08892506,-0.03100369,-0.04011596,0.01319821,0.00161143,0.09020907,-0.03127147,0.0254478,0.05866047,-0.03549645,-0.05955174,0.00016563,-0.01517878,0.00487872,-0.02989758,0.0168703,0.03532359,-0.0144584,-0.00647261,-0.08947013,0.01861626,-0.0941801,-0.07890694,0.03809191,-0.01222154,0.01065369,0.02000292,-0.02337703,0.01449855,0.03079566,-0.02156918,0.00445257,-0.02821334,0.01719219,0.08411232,0.0692673,-0.0329709,-0.03963669,-0.01288777,-0.01024922,-0.07323913,0.12086561,-0.00490019,-0.06427148,-0.04957252,-0.00550768,0.01754461,-0.02829364,0.0547595,0.00448665,-0.02249048,-0.01664051,0.05396889,-0.02633964,-0.03540979,-0.01121388,0.10733969,0.00703694,0.0759959,-0.04697094,-0.06109986,0.02320063,0.0208331,-0.04985911,0.0210777,-0.04829958,-0.01019058,-0.03532472,-0.061937,0.02761471,-0.02966793,-0.04297109,-0.05395858,-0.0442693,-0.00329775,0.01963127,0.03910309,-0.02499774,0.10787617,0.02370282,-0.05440321,-0.00045964,-0.01050179,0.00004031,0.01864085,0.00177536,0.08781406,0.02291385,0.0168155,0.00315053,0.04277246,-0.01598049,-0.051093,0.01068075,0.02458434,0.02387277,0.02180278,-0.02083941,0.03565198,-0.06296436,-0.06411363,-0.20147826,-0.00813445,0.03934141,-0.03775889,0.04753676,-0.02321057,0.03042263,-0.01461419,0.06015676,0.10391514,0.08005838,0.00911302,-0.05073974,0.00860064,0.00101156,0.01940299,-0.00535809,-0.0080659,-0.04606966,0.02386871,-0.0338202,0.00608471,0.00603576,-0.02529185,0.05951659,-0.05169896,0.15016924,0.0011903,0.06028216,0.04181015,0.00922392,0.01906221,-0.00568017,-0.10471831,0.03114651,0.07532415,-0.02370665,0.01478866,-0.00861943,-0.02093805,0.06618604,0.03527291,-0.05964419,-0.05337987,-0.0034212,-0.00987148,-0.02900983,0.01058707,-0.01665042,0.07050958,0.00909375,0.05262376,-0.03535799,-0.02158154,-0.02293177,-0.07419969,-0.02489874,-0.03134625,0.00132035,0.04764324,0.01215787,-0.0644868,-0.01929124,0.052998,0.01890606,-0.03557202,0.00338377,-0.04955215,-0.01503238,0.03774082,0.00934477,0.12224152,0.05265822,-0.0520642,0.00893628,0.0402949,0.03053928,-0.00027965,0.01301886,-0.00516156,0.02964174,0.04597936,0.02155991,0.01848569,0.02954389,-0.00613251,-0.01541975,0.01193054,0.06878532,-0.06569342,-0.05990156,-0.02258157,-0.0188516,0.07809786,0.09481889,-0.02884407,-0.29570168,0.04325548,-0.03253357,0.03719383,0.05916597,0.03011108,0.07673664,0.05966927,-0.03255776,0.02410535,-0.07720945,-0.01535327,0.01047175,-0.01859236,-0.01607108,-0.01563914,0.04983782,-0.03638923,0.06973322,-0.02858748,-0.00132376,0.05818254,0.17958689,0.00118428,0.07215356,0.00252702,0.0210232,0.0531099,0.07798328,0.06861281,0.02475602,-0.05282542,0.03091403,-0.03926619,0.07252952,0.03215539,-0.06458038,-0.01939141,0.0410203,-0.02631978,-0.00556265,0.05955858,-0.10774281,0.00000207,0.10570382,-0.0061467,-0.01432845,-0.10881818,-0.0074241,0.06268119,0.02769732,0.0370507,-0.02254215,-0.01558725,0.02416468,0.05326061,0.03202328,0.00015132,-0.04383551,-0.01895402,0.02715143,-0.01925652,0.13203679,0.11155043,0.01682071],"last_embed":{"hash":"a587b12f62a2b7b76928d3d92c1da754abb6cd2c772c86c99b3a0dfc8c4fcc53","tokens":429}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.10316037,-0.00216731,0.01469399,0.01684068,-0.03378638,0.02872161,0.06215016,-0.03293308,-0.01449192,-0.02892852,0.00211281,0.02724522,0.04259426,-0.00331097,-0.03688009,0.05051478,-0.01464886,0.06081678,-0.00791341,0.00141141,0.01560896,-0.01183124,0.05290178,0.03261389,-0.00988461,-0.01049281,0.0430001,-0.02903542,0.02376746,0.03467455,0.03502661,-0.06627451,0.0020425,-0.00561415,-0.01696361,0.0398859,0.04192627,0.00522449,-0.0610184,0.01091936,0.01126557,0.0090934,-0.04764102,-0.00287817,-0.13316578,0.03351767,-0.00908598,-0.06432971,-0.02732944,0.01759925,-0.01275227,-0.05821386,0.03297851,0.04344607,0.00325598,0.04520968,-0.02034526,-0.06521548,-0.01726252,0.02620094,-0.02045681,0.00187553,0.07316118,0.02418528,-0.01314443,0.03508489,0.04351917,-0.08901638,-0.02914005,-0.02630485,-0.05105671,-0.02957801,-0.00331583,0.01536607,-0.00185427,-0.0810192,0.06018922,0.00118874,0.00237932,-0.00346512,-0.08337226,0.00743903,-0.02037097,0.06882779,-0.00979065,-0.0252383,-0.00334415,-0.0235921,0.01904046,0.01230023,0.01552023,0.03271018,-0.02578082,0.00684352,0.03401599,-0.00310001,-0.00839484,0.00931193,0.02139145,-0.04585518,0.02096967,0.01960753,-0.07447304,-0.00983597,-0.01948252,-0.01911053,-0.02690498,0.04272256,-0.03098312,-0.01021268,-0.02032179,-0.00170252,-0.03389657,-0.03453055,-0.01065241,0.00132195,-0.04614669,0.0303999,0.0303195,0.08714706,0.02949469,0.02810788,0.01439432,-0.00719602,-0.01578836,0.0089931,-0.0253466,-0.04495333,0.04009346,-0.05008364,0.00600494,-0.00957126,0.0068363,0.0222617,0.02565696,0.00531538,0.00642288,-0.02988272,-0.01196156,-0.0102312,0.02159318,0.01799956,0.02319621,-0.01196611,0.04090658,0.00608018,-0.01704068,-0.04522617,-0.01782955,0.05738935,-0.01950291,-0.00886784,-0.02402196,-0.05727483,-0.05656212,0.03173801,0.02106628,0.03325187,0.01779997,-0.00067921,-0.02661076,-0.00163384,0.06605634,0.03158486,0.05755049,0.01032829,0.07192025,0.02402,-0.02461871,-0.02357407,-0.00221789,0.04442315,0.04650191,0.03132988,0.04693397,0.01869805,0.06437156,0.09381002,-0.01911797,-0.02841996,-0.00813008,-0.03950322,0.01683031,0.04020431,0.01104158,0.01630729,-0.03470498,0.07283203,0.0059532,-0.02592258,0.0163204,-0.00605263,0.00729381,-0.04433106,0.02462274,0.0071538,-0.01370625,-0.00448027,-0.0205784,0.00531506,-0.00990882,0.00608107,-0.01915811,-0.05455542,0.04933864,0.03054557,-0.01463235,-0.05052223,0.01402242,0.0641989,0.06088061,-0.01023697,0.05279136,-0.00653215,-0.03293357,0.05281776,-0.02960997,0.05774071,0.01108612,-0.00675077,0.0092393,-0.0737963,-0.08014006,-0.0269631,-0.0142882,-0.00440474,-0.03075484,0.02917413,0.03699813,0.02277643,0.00363351,0.07372431,-0.0222804,-0.04352508,-0.03388127,-0.00709651,-0.02888019,0.0290952,0.03464593,-0.00321584,0.01273001,0.02583728,0.01199941,0.04033348,-0.02328416,0.01090826,-0.04674769,-0.01497975,-0.03988663,0.04037859,0.06319856,0.02330438,0.02046754,0.09193654,0.00896147,-0.04410581,-0.06401205,-0.00048494,-0.0151772,-0.02284029,0.02283231,0.00199428,-0.01823626,0.021843,0.00421599,-0.0069139,0.01638527,0.02539019,-0.01695181,0.0569723,-0.09300593,-0.04064369,0.04675878,0.00197409,-0.01319094,-0.01736856,-0.05719832,-0.03616237,0.03868773,-0.01013714,-0.05198331,-0.04717014,-0.0008907,0.01845634,-0.09285755,0.00728739,0.01798408,0.00940006,0.05909738,-0.01923936,0.02283623,0.00056298,-0.03510655,0.02654393,-0.01063429,-0.0216798,-0.01753698,0.02247867,0.03918793,-0.08772899,-0.05203704,0.02080649,0.02946758,-0.0104308,0.05005967,0.02250219,0.00196656,0.01037439,-0.04732743,-0.02140934,-0.01787859,-0.0168435,0.03677816,0.0342479,-0.00839035,0.04717065,0.02441891,0.00501903,0.00468985,-0.02235233,-0.04631295,-0.01358353,-0.00740007,0.03639765,0.03417481,-0.08181512,0.04346945,0.00552964,0.00417321,0.00613771,-0.06749484,-0.02146649,-0.02200147,0.03612399,0.01306523,0.00049535,-0.03555501,-0.0863236,0.04098484,-0.04227613,0.03873933,-0.05591483,0.00067652,-0.06727725,0.00878733,0.06774952,0.01224178,0.01603317,-0.03966361,-0.04333651,-0.00132306,-0.01676792,0.00430325,0.02291222,0.01642257,0.00312913,0.01665089,0.01683659,-0.03014833,0.00962748,0.01970289,-0.0318883,-0.02640476,-0.01871677,-0.06104647,-0.02160613,0.02815775,0.0797669,-0.04954601,-0.00482527,0.00802944,0.01007659,-0.04502559,-0.01589928,0.01171025,-0.00888774,-0.01597785,0.0059734,-0.02931245,-0.00970028,-0.02324044,-0.05895297,-0.02377308,-0.04807695,-0.0657073,-0.00988062,-0.00605995,-0.01397979,-0.00484396,-0.0441385,-0.0089245,0.00788242,-0.06071267,0.02565093,0.01463121,-0.0260592,0.01560898,-0.01980281,-0.0271828,0.02707302,0.0630107,0.01294534,0.01293658,-0.0257081,-0.02664968,0.01977842,-0.02906929,0.08292345,-0.01467614,-0.00950256,-0.04917819,0.08866615,-0.019216,0.02412257,0.00315455,0.02736286,0.05709426,0.03818174,-0.00875132,0.02091863,0.05368494,-0.01018341,0.0283353,0.01516191,0.00649177,-0.02026195,0.00479438,0.02029591,0.02747422,0.00609098,0.02634693,0.00453946,-0.09682834,-0.05646217,0.01682674,-0.0295735,0.04336308,0.02521705,0.03071683,0.01686484,-0.01851786,-0.03218915,-0.01085401,-0.0666228,0.01930994,0.04177037,0.02716501,0.00838762,-0.04758418,0.03521089,0.01318093,-0.01789792,-0.13708113,-0.0490271,0.00852304,-0.01567141,0.0025912,-0.02539526,-0.01857243,0.02005148,-0.12613879,0.04598518,0.00921759,0.03570838,0.02809283,-0.02692885,-0.00507323,-0.06967647,-0.00964835,-0.0001978,0.03172503,0.02635862,0.00451617,-0.02891764,0.09914429,0.00495992,0.00380519,-0.02070157,-0.04287094,-0.00870821,0.00903132,0.04240633,-0.00038676,-0.00666632,-0.03331115,0.05020376,0.01505052,-0.03798998,0.01527565,-0.00965826,0.01462143,0.05059363,0.01883596,-0.07240102,0.03621705,0.01531504,-0.02870055,0.06298769,-0.00858361,0.03373389,0.01076896,0.06759109,-0.02681754,0.02761524,0.07146136,0.02939519,-0.05184813,0.0092976,0.06811523,-0.04188301,0.0035864,-0.08984334,0.06081384,0.02827337,-0.01711718,-0.04197731,0.01459042,-0.01061487,0.0054012,-0.03426914,-0.03552059,0.05981142,-0.01752255,-0.00458075,0.03643182,-0.00830245,-0.01609085,0.07020209,0.02818929,0.01931806,-0.00944297,-0.01119968,-0.00671899,-0.01402717,0.00640341,-0.00512299,0.02986649,-0.00419377,-0.02077511,0.02038647,0.03193846,0.0232462,0.01978841,0.07399166,-0.03309599,0.00691358,0.01493801,-0.04432558,-0.00102357,0.03063905,0.01271934,0.06823193,0.00252988,-0.03327266,-0.00791306,0.02301035,0.00914381,-0.04321742,-0.00835518,0.04116821,-0.02697352,0.00037175,-0.02414258,-0.01976807,-0.01941981,0.00852194,-0.04751058,-0.01306176,-0.03776542,0.03906398,-0.06575692,-0.01565732,0.03115442,-0.01179136,-0.05322035,0.00317969,-0.00608368,-0.01154243,0.07155706,-0.0411441,0.02095583,0.00656308,0.02979199,-0.02834175,-0.07689247,-0.02651141,0.04545784,-0.00927944,0.04220016,0.03906459,0.00143933,-0.06739479,-0.01902788,0.03763429,-0.03340549,0.00390556,0.022645,0.06261721,0.033656,-0.02446291,-0.00648065,-0.05420219,-0.0233683,-0.02113774,-0.0189215,-0.06729264,-0.01094252,-0.08076761,0.00461227,0.05671014,0.04168247,0.0411725,-0.00919162,-0.01694618,0.03791304,-0.01550904,0.00837369,-0.00953845,0.01618818,0.00428558,-0.00840433,0.01402522,0.00704775,0.00785,0.01168556,0.0119809,-0.01994119,-0.00662174,0.03099636,-0.03797211,-0.05125178,-0.01402414,0.06220781,-0.00753779,0.01339972,0.07560378,-0.06586868,-0.09787794,-0.04676401,-0.008706,0.03189318,-0.02589109,-0.01279754,0.04246229,0.01440638,0.01486724,-0.01873722,-0.02494865,0.03922014,-0.06654655,-0.01949384,-0.02007859,-0.01140636,-0.02030934,-0.0450244,-0.01597025,-0.01798917,0.0500416,-0.02241757,0.0453423,-0.08452531,-0.05075659,-0.01477743,-0.01687307,-0.05127272,-0.00679793,0.00292438,0.03546774,-0.03034248,-0.00838179,-0.0467841,-0.03231043,0.02669239,-0.00884236,0.03479347,0.04752462,-0.01627992,0.03186517,0.03387605,-0.0122371,-0.08771627,0.02516117,-0.00252061,-0.01124694,0.05157229,-0.02610351,-0.0008468,0.02148644,-0.04805319,-0.0078648,-0.00618137,0.06739679,0.00940302,0.01356836,0.08441088,-0.02645306,0.01963266,-0.04446416,0.03066274,0.01569027,0.01877633,0.02477525,-0.00976134,0.01023619,-0.04079754,0.01206596,-0.00616501,0.02469829,0.08655874,-0.04411863,-0.06371466,-0.02860256,-0.00731517,-0.0105078,0.01927807,-0.00698406,-0.0254373,0.0555156,-0.01861293,0.00860816,-0.02628788,0.01696688,0.05370292,0.03769289,-0.04891107,-0.00994733,-0.00629095,0.01479166,-0.00756438,0.0415474,0.00747861,-0.05636073,-0.00183987,0.09936932,-0.00395911,-0.03025557,-0.04147734,0.00065812,0.02589007,-0.0216549,0.0053157,-0.00253496,-0.02172891,-0.02245338,-0.01165265,0.00860722,-0.03803418,-0.00533002,0.01909729,0.06707413,-0.01925395,0.05366265,-0.03985028,0.02636742,0.0005918,-0.01670205,-0.0369312,0.01279102,7e-7,0.00263444,-0.06963319,0.04400845,-0.00041873,-0.01508191,-0.02033322,-0.0083047,0.0226244,0.07544684],"last_embed":{"hash":"1vm96m9","tokens":592}}},"text":null,"length":0,"last_read":{"hash":"1vm96m9","at":1748397845255},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#实际测试","lines":[74,137],"size":1290,"outlinks":[{"title":"序列化","target":"序列化","line":22},{"title":"序列化","target":"序列化","line":52}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#实际测试#Python的反序列化实验": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.12791482,-0.00304797,-0.01810342,-0.04438559,-0.02404032,-0.00085611,-0.01152951,0.01373501,0.06994826,0.01475025,-0.00191225,-0.119286,0.04042201,0.04465862,0.0646293,-0.01971105,-0.03017525,-0.00567811,-0.06306705,0.00280464,0.09556039,-0.03643031,-0.0293948,-0.08783889,-0.00158856,0.04415167,0.01752477,0.01366247,-0.03114297,-0.16718853,0.06475312,-0.00724108,0.02289127,-0.01433908,0.0147693,0.02131808,0.03971389,0.0356656,-0.0060292,0.0516458,0.0339777,0.02756224,0.0322091,-0.04461115,-0.02723466,-0.0641764,-0.08629452,-0.00596312,0.01341212,-0.03530006,-0.02065145,-0.01433522,-0.05799758,-0.01958071,-0.0160239,0.00089831,0.01413354,0.00775599,0.0387807,-0.01497731,0.01954857,0.00650976,-0.19050387,0.08169587,0.01298432,-0.02669467,-0.02915546,0.00552502,0.02666177,0.0218993,-0.09949972,0.0251791,0.01499062,0.06889557,0.03390798,-0.04036096,0.04005484,-0.02738821,-0.03430562,-0.04805804,-0.04857106,0.04859624,-0.03833432,-0.00600111,-0.00295267,0.01224304,0.00039118,-0.03656055,0.02999876,-0.0420101,0.03772004,-0.0776508,-0.04269743,0.0552136,-0.02998473,0.02780411,0.03078218,0.04177798,-0.10275418,0.11808769,-0.05139083,0.01865745,-0.01770731,-0.02438527,-0.00906726,-0.0388446,-0.02080707,-0.03266397,-0.05620972,-0.03676775,-0.0080971,-0.06390222,0.07454392,-0.01779794,0.00018166,0.04267808,-0.00552168,-0.02294083,-0.03970795,0.00904941,0.01651841,-0.02334411,0.08909605,-0.03096876,-0.04032414,0.01384561,0.00177807,0.09043638,-0.03148036,0.02538984,0.05848565,-0.03574633,-0.05944101,0.00032096,-0.01485198,0.00501319,-0.02986777,0.01663728,0.03531776,-0.01442746,-0.00646669,-0.08941549,0.0186539,-0.09383992,-0.07859294,0.03802482,-0.01208908,0.01091263,0.01957733,-0.02347416,0.01444103,0.03082993,-0.0215154,0.00448297,-0.02835163,0.01727016,0.08417,0.06880785,-0.03273935,-0.03958914,-0.01292601,-0.0105144,-0.07274376,0.12067469,-0.00510723,-0.06426684,-0.04980562,-0.00542442,0.01743533,-0.02815273,0.05455539,0.00427695,-0.0226767,-0.01676895,0.05381365,-0.02622962,-0.03543362,-0.01111589,0.10747041,0.0067632,0.07591054,-0.0469892,-0.06089114,0.02294658,0.02084829,-0.04985513,0.02120213,-0.04799171,-0.01026452,-0.03540331,-0.06186601,0.02784181,-0.02950122,-0.04335501,-0.05384615,-0.04400891,-0.00359415,0.01962689,0.03933197,-0.02503674,0.1079293,0.02361166,-0.05435789,-0.00073554,-0.0101925,0.00026509,0.018461,0.00182745,0.0878315,0.02264765,0.01653232,0.00289243,0.04332671,-0.01615049,-0.05127898,0.01055598,0.02457465,0.02390276,0.02184741,-0.02120584,0.03568516,-0.06313353,-0.06383712,-0.20169562,-0.00757209,0.03943072,-0.03782674,0.04753221,-0.02340466,0.0302166,-0.01444824,0.06002598,0.10382733,0.08021321,0.00882561,-0.0504784,0.00887253,0.00072094,0.01927642,-0.00554253,-0.00809133,-0.04594303,0.02374842,-0.0339095,0.00560652,0.00617028,-0.02494699,0.05958034,-0.05157849,0.15021987,0.00130282,0.06048762,0.0414674,0.00959036,0.01941471,-0.0056663,-0.10503768,0.03094771,0.07557584,-0.02350875,0.01496334,-0.00850224,-0.02091504,0.0661907,0.03552065,-0.05972049,-0.05334286,-0.00310787,-0.00977094,-0.02866918,0.01077582,-0.01698857,0.07034817,0.00922134,0.05268628,-0.03555091,-0.0216267,-0.02294026,-0.07448597,-0.02443597,-0.03134744,0.00132044,0.0473255,0.01210354,-0.06472836,-0.0194381,0.05280129,0.01904836,-0.03577667,0.00322365,-0.04945469,-0.01464338,0.03748096,0.00930778,0.12194257,0.05281703,-0.05207805,0.008779,0.04041898,0.03023161,-0.00009164,0.0127639,-0.00521919,0.0298982,0.04616173,0.02141515,0.01849546,0.02985506,-0.00600508,-0.01568217,0.01216343,0.06884247,-0.06560692,-0.05973356,-0.02274465,-0.0185313,0.07875083,0.09524003,-0.02873248,-0.29570624,0.04338782,-0.03259093,0.0374475,0.05913728,0.03017896,0.07681444,0.05977083,-0.03214507,0.02401867,-0.07726148,-0.01528991,0.01025001,-0.01845033,-0.0162645,-0.01550333,0.04971876,-0.03620483,0.06993824,-0.02881486,-0.00161218,0.05794945,0.17956404,0.00133705,0.07207456,0.00253343,0.02103777,0.05300452,0.07774098,0.0688803,0.02490264,-0.05277913,0.03105459,-0.03926892,0.0725108,0.03238181,-0.06447379,-0.01939502,0.04086037,-0.02650648,-0.00548991,0.05954837,-0.10784721,-0.00003928,0.10573012,-0.00650393,-0.01425667,-0.10892909,-0.00767517,0.06267011,0.02784247,0.03706765,-0.02260786,-0.01549648,0.02406196,0.05306019,0.03201539,0.00027084,-0.04375814,-0.01885723,0.02737944,-0.019373,0.13192198,0.11156738,0.01663039],"last_embed":{"hash":"1a641bc05fe348bad86abfb56b92f90353b2702e52185c7f08bdac2faa38d34f","tokens":429}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.10349413,-0.00431274,0.01561686,0.01513595,-0.03397256,0.02792427,0.05716555,-0.03364623,-0.0160604,-0.02972561,0.00475155,0.03003219,0.04786091,-0.00480142,-0.0375097,0.04986082,-0.01453144,0.05955518,-0.00731448,0.00404549,0.01509685,-0.01322568,0.05119115,0.03379689,-0.0129649,-0.01124758,0.04294672,-0.02957358,0.0266003,0.03245354,0.03187855,-0.06654913,0.00347532,-0.00394013,-0.01181418,0.04119105,0.04222312,0.00679645,-0.06017713,0.0067102,0.01234343,0.00921258,-0.04776011,-0.00313648,-0.13310944,0.03329612,-0.00892841,-0.06674324,-0.02499239,0.01749393,-0.0108948,-0.05858592,0.0303659,0.04374159,0.00429456,0.04385092,-0.01856566,-0.06508981,-0.01800837,0.02625398,-0.02054304,0.0046735,0.07276334,0.02329874,-0.01872279,0.03514208,0.04556878,-0.08777667,-0.03063309,-0.02651563,-0.04744354,-0.02867384,-0.00126854,0.01513944,-0.0009258,-0.08132441,0.05795881,0.00109814,0.00099075,-0.00144895,-0.0811581,0.00801095,-0.02118859,0.06579924,-0.01116678,-0.02320632,-0.00635473,-0.02261115,0.02038666,0.01139348,0.01358803,0.03211089,-0.02603577,0.00718102,0.03333532,0.00009731,-0.00730689,0.00844155,0.02361674,-0.04575609,0.02070702,0.01849774,-0.07493786,-0.01083555,-0.02379745,-0.01531864,-0.02683927,0.04380151,-0.02942665,-0.01220891,-0.02023841,-0.00196242,-0.03332692,-0.03328751,-0.00806846,-0.00184134,-0.04554623,0.03527629,0.0287452,0.08709905,0.0250801,0.02890849,0.0125499,-0.00549921,-0.01537169,0.00876614,-0.02425981,-0.04736375,0.03803175,-0.0519898,0.00475004,-0.0087949,0.00821669,0.02628251,0.0264389,0.0058915,0.00692104,-0.03037023,-0.0182802,-0.0087914,0.02547434,0.01932756,0.02372747,-0.01201957,0.03815223,0.00516747,-0.01558962,-0.04211546,-0.01623659,0.05526614,-0.01703714,-0.01075179,-0.02316714,-0.05586905,-0.05767005,0.03277407,0.02005389,0.03115972,0.01730617,-0.00115443,-0.02811523,0.00124239,0.06836236,0.03080564,0.05446882,0.00989957,0.07198559,0.02304307,-0.02399034,-0.02640681,-0.00220791,0.04417978,0.04671691,0.03157322,0.04604851,0.02228533,0.06148157,0.09443063,-0.01750435,-0.02902311,-0.0124103,-0.03846821,0.01723046,0.03985359,0.01150542,0.01603428,-0.0341052,0.07492901,0.00193002,-0.02600145,0.01618847,-0.00517114,0.00592876,-0.04734372,0.02475837,0.00593784,-0.0159501,-0.00369089,-0.02093896,0.00309602,-0.00796555,0.00564776,-0.02031443,-0.05604874,0.05120922,0.03105142,-0.01591592,-0.04969656,0.01540959,0.06112602,0.05999504,-0.01170751,0.05189096,-0.00656898,-0.03247328,0.05509982,-0.02847778,0.05480918,0.009383,-0.00529745,0.00781831,-0.07211486,-0.07893525,-0.02873832,-0.01325359,-0.00155921,-0.02976104,0.03009832,0.0368111,0.0234107,0.00293077,0.07041372,-0.0233237,-0.04280982,-0.03428621,-0.00997071,-0.03236599,0.02931618,0.03666535,-0.00497513,0.01186136,0.02546852,0.01183264,0.03806418,-0.02578695,0.01070506,-0.04413844,-0.01437645,-0.04045169,0.04001534,0.06705521,0.02365805,0.02179271,0.09145208,0.00679347,-0.04396292,-0.06171248,0.00103402,-0.01486273,-0.02244446,0.02296476,0.00109799,-0.01687839,0.0202462,0.00497849,-0.0052921,0.01897534,0.02791406,-0.01442993,0.0564413,-0.09140546,-0.04067843,0.04635444,0.00148401,-0.01399161,-0.01956276,-0.05713256,-0.03465549,0.04338489,-0.00962284,-0.05381846,-0.04864337,0.00143761,0.01941933,-0.09354841,0.00511744,0.01653044,0.01011318,0.06028474,-0.01765276,0.02237371,-0.00054091,-0.03420061,0.02382093,-0.00829729,-0.02194134,-0.016328,0.02336304,0.0401965,-0.08777289,-0.05323685,0.01866398,0.03192205,-0.01222774,0.04878987,0.02171817,0.0019309,0.01139047,-0.04405346,-0.01965535,-0.01781727,-0.0138891,0.03933735,0.03451618,-0.01115243,0.04534369,0.0265265,0.0023213,0.00696218,-0.02333475,-0.0454188,-0.01289411,-0.00701884,0.03610807,0.03593791,-0.0797532,0.0433698,0.0082443,0.00132331,0.00685266,-0.06677321,-0.02331993,-0.02564539,0.03849315,0.01122894,0.00021846,-0.03595298,-0.08835044,0.04138853,-0.04246218,0.03558603,-0.05557123,0.001518,-0.06912644,0.00689643,0.06633785,0.01407961,0.01508344,-0.03685852,-0.04399801,-0.00408996,-0.01707688,0.00338252,0.02306842,0.01598762,0.00412439,0.01795176,0.01709407,-0.02986903,0.01039101,0.01877961,-0.03024048,-0.02507155,-0.02003694,-0.06041129,-0.02407129,0.02649311,0.07808145,-0.04909837,-0.00441352,0.0079413,0.0109925,-0.04224928,-0.01685029,0.01038877,-0.00868174,-0.01590779,0.0044972,-0.02788437,-0.01098422,-0.02432543,-0.05742316,-0.0190456,-0.04776472,-0.06633235,-0.01102513,-0.00704957,-0.01251905,-0.00186269,-0.04424795,-0.00812476,0.00631476,-0.05802825,0.02574938,0.01392881,-0.0250167,0.01485796,-0.02061266,-0.0280132,0.02553555,0.06373524,0.01287031,0.0108396,-0.02880787,-0.02402837,0.01854258,-0.02728222,0.0824721,-0.01492773,-0.00817351,-0.0499447,0.08999795,-0.02279169,0.02262589,0.00240722,0.0278991,0.05774844,0.04160948,-0.00901279,0.01903198,0.05367421,-0.00937045,0.02512119,0.01331051,0.00591923,-0.02116985,0.00582537,0.02223105,0.02539113,0.00703663,0.02897557,0.00472928,-0.09530566,-0.05901927,0.01694017,-0.028014,0.04313936,0.02798601,0.02980491,0.01784181,-0.01831865,-0.03276584,-0.01343818,-0.06848965,0.02080958,0.0411182,0.03001263,0.01575601,-0.0461496,0.03113118,0.01516275,-0.01712728,-0.13880263,-0.04642893,0.00547374,-0.01446346,0.00235939,-0.0258335,-0.02128264,0.02264615,-0.12504148,0.0459936,0.00910249,0.03705513,0.02631781,-0.02774002,-0.00480801,-0.07103113,-0.00937827,-0.00285344,0.0320385,0.02613186,0.00299648,-0.030969,0.09867651,0.00565695,0.00366362,-0.02181785,-0.0429393,-0.01020744,0.00819712,0.03900183,-0.0018596,-0.00732718,-0.03729191,0.04805454,0.01647083,-0.03709773,0.01418943,-0.00950927,0.01815917,0.05023371,0.018579,-0.07209279,0.03728284,0.01818307,-0.03134178,0.06365056,-0.01130947,0.03206809,0.01102468,0.06916719,-0.02822086,0.02834867,0.07213539,0.03051662,-0.05243482,0.00806877,0.06721111,-0.04176278,0.0041006,-0.08886589,0.0614403,0.02365298,-0.01745212,-0.04087941,0.01833262,-0.01318325,0.00527004,-0.03413705,-0.03548904,0.06086003,-0.01539698,-0.00357709,0.03979827,-0.00663624,-0.01603793,0.07002645,0.02675774,0.01841248,-0.00633224,-0.01171858,-0.00657316,-0.01523943,0.00305375,-0.00299093,0.02999144,-0.0038918,-0.02061041,0.01893358,0.03015295,0.0239827,0.02013071,0.06996686,-0.0325942,0.00678036,0.01407383,-0.04355234,-0.00282564,0.03079944,0.01228469,0.06760082,0.00303608,-0.03334691,-0.00701862,0.02033129,0.00854313,-0.04356731,-0.00897449,0.03781551,-0.02652498,-0.00253872,-0.02450855,-0.01751527,-0.01815951,0.00639262,-0.04807366,-0.01622326,-0.03868712,0.03980779,-0.06808756,-0.01522352,0.03078376,-0.01121439,-0.05211339,0.00238779,-0.0055676,-0.01113005,0.07311327,-0.03976601,0.02000197,0.00705232,0.02809451,-0.02898796,-0.07924566,-0.02717389,0.04949846,-0.0074868,0.0440436,0.03809193,0.00258546,-0.0666872,-0.01997026,0.03850582,-0.0296541,0.00836729,0.02200948,0.06388707,0.03168308,-0.02324308,-0.00705785,-0.05736714,-0.02512383,-0.02031242,-0.02020743,-0.0658641,-0.00987962,-0.07858934,0.00610241,0.05575256,0.04428906,0.04249868,-0.00718554,-0.01469566,0.04106959,-0.01412728,0.00809928,-0.01069554,0.01981754,0.00545462,-0.00828991,0.01700378,0.00878003,0.00966882,0.01314155,0.00945058,-0.02141323,-0.00700104,0.02903772,-0.03820265,-0.04779239,-0.01231632,0.06283234,-0.01113141,0.01472917,0.07589059,-0.06504533,-0.10135475,-0.04864037,-0.00824566,0.02942108,-0.02914033,-0.01234725,0.04306537,0.01490872,0.01753529,-0.01873989,-0.02965642,0.03676304,-0.06897901,-0.01881994,-0.01772278,-0.0143815,-0.02109918,-0.04396066,-0.01936046,-0.01523786,0.0506156,-0.02341827,0.04535445,-0.08435702,-0.05027798,-0.01293662,-0.01480547,-0.05311022,-0.00899139,0.0003366,0.03629661,-0.03195488,-0.01223266,-0.04675113,-0.03233726,0.03002606,-0.00717564,0.03679501,0.04603209,-0.01854464,0.03355405,0.03492529,-0.01211833,-0.08851817,0.02442469,-0.00016457,-0.01326324,0.04929701,-0.02542344,0.00033007,0.02097231,-0.04506521,-0.00822324,-0.00498414,0.06675521,0.00973432,0.01453326,0.08774757,-0.02624999,0.0183268,-0.04100615,0.03217367,0.01391316,0.01992759,0.02795546,-0.01030331,0.00783432,-0.04028462,0.0116934,-0.00699322,0.02753595,0.08717573,-0.0433653,-0.06386822,-0.03075727,-0.00874987,-0.01299732,0.02076865,-0.00414763,-0.02351609,0.05337999,-0.01751748,0.00679692,-0.02747759,0.01468771,0.05480226,0.03843536,-0.04825063,-0.01019905,-0.00517883,0.01336816,-0.00797585,0.0425361,0.00993995,-0.05498631,-0.00212493,0.09977946,-0.00505595,-0.02853421,-0.04196656,0.00241339,0.02597748,-0.02200228,0.00803512,-0.00257832,-0.02068545,-0.02192507,-0.00997437,0.01188055,-0.04047582,-0.00622063,0.01686517,0.06831224,-0.01724377,0.05286309,-0.03884985,0.02396856,-0.00257458,-0.01905015,-0.03700504,0.01402886,7e-7,0.00554028,-0.07073911,0.04211019,-0.00023158,-0.01595534,-0.01966207,-0.00494868,0.02485577,0.0760257],"last_embed":{"hash":"190ah76","tokens":592}}},"text":null,"length":0,"last_read":{"hash":"190ah76","at":1748397845314},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#实际测试#Python的反序列化实验","lines":[76,137],"size":1282,"outlinks":[{"title":"序列化","target":"序列化","line":20},{"title":"序列化","target":"序列化","line":50}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#---frontmatter---": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#---frontmatter---","lines":[1,9],"size":136,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#简介": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#简介","lines":[10,15],"size":274,"outlinks":[{"title":"Jenkins","target":"Jenkins","line":2},{"title":"反序列化","target":"反序列化","line":3},{"title":"反序列化","target":"反序列化","line":3},{"title":"反序列化","target":"反序列化","line":4}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#简介#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#简介#{1}","lines":[11,15],"size":269,"outlinks":[{"title":"Jenkins","target":"Jenkins","line":1},{"title":"反序列化","target":"反序列化","line":2},{"title":"反序列化","target":"反序列化","line":2},{"title":"反序列化","target":"反序列化","line":3}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#相关漏洞": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#相关漏洞","lines":[16,21],"size":54,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#相关漏洞#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#相关漏洞#{1}","lines":[17,17],"size":15,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#相关漏洞#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#相关漏洞#{2}","lines":[18,20],"size":27,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#相关漏洞#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#相关漏洞#{3}","lines":[21,21],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#常用工具": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#常用工具","lines":[22,26],"size":51,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#常用工具#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#常用工具#{1}","lines":[23,23],"size":15,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#常用工具#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#常用工具#{2}","lines":[24,24],"size":8,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#常用工具#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#常用工具#{3}","lines":[25,25],"size":15,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#常用工具#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#常用工具#{4}","lines":[26,26],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#漏洞环境#可能出现的位置": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#漏洞环境#可能出现的位置","lines":[29,41],"size":279,"outlinks":[{"title":"session","target":"session","line":2},{"title":"token","target":"token","line":2},{"title":"反序列化","target":"反序列化","line":3},{"title":"序列化","target":"序列化","line":3},{"title":"json","target":"json","line":4},{"title":"序列化","target":"序列化","line":5},{"title":"json","target":"json","line":6},{"title":"序列化","target":"序列化","line":8},{"title":"Java","target":"Java","line":11}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#漏洞环境#可能出现的位置#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#漏洞环境#可能出现的位置#{1}","lines":[30,30],"size":31,"outlinks":[{"title":"session","target":"session","line":1},{"title":"token","target":"token","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#漏洞环境#可能出现的位置#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#漏洞环境#可能出现的位置#{2}","lines":[31,32],"size":65,"outlinks":[{"title":"反序列化","target":"反序列化","line":1},{"title":"序列化","target":"序列化","line":1},{"title":"json","target":"json","line":2}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#漏洞环境#可能出现的位置#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#漏洞环境#可能出现的位置#{3}","lines":[33,34],"size":45,"outlinks":[{"title":"序列化","target":"序列化","line":1},{"title":"json","target":"json","line":2}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#漏洞环境#可能出现的位置#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#漏洞环境#可能出现的位置#{4}","lines":[35,35],"size":10,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#漏洞环境#可能出现的位置#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#漏洞环境#可能出现的位置#{5}","lines":[36,36],"size":33,"outlinks":[{"title":"序列化","target":"序列化","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#漏洞环境#可能出现的位置#{6}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#漏洞环境#可能出现的位置#{6}","lines":[37,38],"size":45,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#漏洞环境#可能出现的位置#{7}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#漏洞环境#可能出现的位置#{7}","lines":[39,40],"size":29,"outlinks":[{"title":"Java","target":"Java","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#漏洞环境#可能出现的位置#{8}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#漏洞环境#可能出现的位置#{8}","lines":[41,41],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#漏洞环境#漏洞成因/环节": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#漏洞环境#漏洞成因/环节","lines":[42,54],"size":222,"outlinks":[{"title":"反序列化","target":"反序列化","line":3},{"title":"序列化","target":"序列化","line":4},{"title":"反序列化","target":"反序列化","line":7}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#漏洞环境#漏洞成因/环节#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#漏洞环境#漏洞成因/环节#{1}","lines":[43,49],"size":148,"outlinks":[{"title":"反序列化","target":"反序列化","line":2},{"title":"序列化","target":"序列化","line":3},{"title":"反序列化","target":"反序列化","line":6}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#漏洞环境#漏洞成因/环节#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#漏洞环境#漏洞成因/环节#{2}","lines":[50,50],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#漏洞环境#漏洞成因/环节#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#漏洞环境#漏洞成因/环节#{3}","lines":[51,53],"size":54,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#漏洞环境#漏洞成因/环节#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#漏洞环境#漏洞成因/环节#{4}","lines":[54,54],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#漏洞环境#漏洞原理": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#漏洞环境#漏洞原理","lines":[55,73],"size":492,"outlinks":[{"title":"PHP","target":"PHP","line":2},{"title":"Python","target":"Python","line":2},{"title":"序列化","target":"序列化","line":6},{"title":"反序列化","target":"反序列化","line":7},{"title":"#Python的反序列化实验","target":"#Python的反序列化实验","line":8},{"title":"反射（reflection）机制","target":"https://xz.aliyun.com/t/7029/","line":10},{"title":"Java","target":"Java","line":10},{"title":"变量","target":"变量","line":13},{"title":"Java","target":"Java","line":15}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#漏洞环境#漏洞原理#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#漏洞环境#漏洞原理#{1}","lines":[56,61],"size":174,"outlinks":[{"title":"PHP","target":"PHP","line":1},{"title":"Python","target":"Python","line":1},{"title":"序列化","target":"序列化","line":5},{"title":"反序列化","target":"反序列化","line":6}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#漏洞环境#漏洞原理#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#漏洞环境#漏洞原理#{2}","lines":[62,62],"size":28,"outlinks":[{"title":"#Python的反序列化实验","target":"#Python的反序列化实验","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#漏洞环境#漏洞原理#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#漏洞环境#漏洞原理#{3}","lines":[63,63],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#漏洞环境#漏洞原理#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#漏洞环境#漏洞原理#{4}","lines":[64,71],"size":271,"outlinks":[{"title":"反射（reflection）机制","target":"https://xz.aliyun.com/t/7029/","line":1},{"title":"Java","target":"Java","line":1},{"title":"变量","target":"变量","line":4},{"title":"Java","target":"Java","line":6}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#漏洞环境#漏洞原理#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#漏洞环境#漏洞原理#{5}","lines":[72,73],"size":4,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#实际测试#Python的反序列化实验#序列化与反序列化": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#实际测试#Python的反序列化实验#序列化与反序列化","lines":[78,100],"size":485,"outlinks":[{"title":"序列化","target":"序列化","line":18}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#实际测试#Python的反序列化实验#序列化与反序列化#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#实际测试#Python的反序列化实验#序列化与反序列化#{1}","lines":[79,94],"size":344,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#实际测试#Python的反序列化实验#序列化与反序列化#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#实际测试#Python的反序列化实验#序列化与反序列化#{2}","lines":[95,96],"size":39,"outlinks":[{"title":"序列化","target":"序列化","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#实际测试#Python的反序列化实验#序列化与反序列化#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#实际测试#Python的反序列化实验#序列化与反序列化#{3}","lines":[97,99],"size":83,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#实际测试#Python的反序列化实验#序列化与反序列化#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#实际测试#Python的反序列化实验#序列化与反序列化#{4}","lines":[100,100],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#实际测试#Python的反序列化实验#系统命令序列化传输": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#实际测试#Python的反序列化实验#系统命令序列化传输","lines":[101,137],"size":778,"outlinks":[{"title":"序列化","target":"序列化","line":25}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#实际测试#Python的反序列化实验#系统命令序列化传输#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#实际测试#Python的反序列化实验#系统命令序列化传输#{1}","lines":[102,116],"size":254,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#实际测试#Python的反序列化实验#系统命令序列化传输#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#实际测试#Python的反序列化实验#系统命令序列化传输#{2}","lines":[117,117],"size":22,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#实际测试#Python的反序列化实验#系统命令序列化传输#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#实际测试#Python的反序列化实验#系统命令序列化传输#{3}","lines":[118,123],"size":140,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#实际测试#Python的反序列化实验#系统命令序列化传输#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#实际测试#Python的反序列化实验#系统命令序列化传输#{4}","lines":[124,125],"size":25,"outlinks":[{"title":"序列化","target":"序列化","line":2}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#实际测试#Python的反序列化实验#系统命令序列化传输#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#实际测试#Python的反序列化实验#系统命令序列化传输#{5}","lines":[126,134],"size":250,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#实际测试#Python的反序列化实验#系统命令序列化传输#{6}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md#实际测试#Python的反序列化实验#系统命令序列化传输#{6}","lines":[135,137],"size":68,"outlinks":[],"class_name":"SmartBlock"},
"smart_sources:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md": {"path":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0665995,-0.03039012,-0.0320711,-0.06960404,0.03305143,-0.03791193,-0.02867184,0.06651863,0.03438744,0.01631835,0.01009577,-0.04957129,0.03022223,0.06191585,0.05354504,0.0172126,-0.00862188,0.0758246,0.00721558,0.04315675,0.08796397,-0.01562169,0.01783143,-0.0914363,-0.00344913,0.03336969,0.03098376,0.00046864,-0.02680105,-0.19513114,0.00563196,-0.00278659,0.03523843,0.01678341,0.01700038,-0.02183521,0.02137866,0.03287796,-0.02738969,0.00731501,0.03036581,0.0007586,0.00374881,-0.01980487,-0.06536857,-0.1102903,-0.02946429,-0.01451905,0.02346163,-0.06765611,-0.03535866,-0.07862574,-0.03799662,-0.00919513,-0.02394756,-0.01707609,0.02205439,0.04045958,0.04073429,0.01024048,0.01979597,0.01945463,-0.20339678,0.05553471,0.0122719,0.01375383,-0.00407974,-0.02723566,-0.0028318,-0.01774417,-0.00941023,0.04149099,-0.04661461,0.03941132,0.06059157,0.05075786,-0.00785786,-0.04050544,0.00119725,-0.0520879,-0.02486136,0.02546909,0.00029858,0.0207544,-0.00868285,0.03037456,0.00745737,0.01882374,0.03894916,0.0009889,-0.01839091,-0.05262978,0.06684262,0.02917804,-0.03637036,0.04735627,0.05340272,0.02666407,-0.05480403,0.12317857,-0.0604801,-0.01959119,-0.00454545,-0.03953808,0.02091224,-0.01957828,-0.03236475,-0.08366702,0.02792594,-0.04011221,-0.04590288,0.00511423,0.06001176,-0.00441717,0.02429381,0.008921,0.00935833,-0.01574473,-0.03041008,0.01252544,-0.04129156,0.01314347,0.09022947,0.00525585,-0.00697608,0.00024111,0.02299677,0.05608715,0.03726538,0.00254609,0.04516919,0.00197553,-0.03803824,-0.03219267,-0.00010586,0.03360872,-0.04422051,0.00818855,0.00057107,-0.01459707,-0.01808054,-0.04152252,-0.01631918,-0.07100246,-0.03544642,0.04553669,-0.04486172,0.00555727,0.01212258,-0.04890346,0.00390724,0.06845737,-0.0055521,-0.01656964,-0.00026879,-0.00109465,0.06483755,0.14914326,-0.05126119,-0.04378508,0.00149867,0.01319066,-0.04849207,0.20174623,0.0287741,-0.03967357,-0.04020203,0.00856103,-0.02166355,-0.02459949,0.00224847,-0.01051103,0.03223695,-0.00484964,0.02612489,-0.04556865,-0.01717075,-0.03056096,0.00632733,0.05580384,0.0485117,-0.00647106,-0.09419917,0.04687899,0.00127275,-0.10093572,-0.08206073,-0.04214654,0.00061659,-0.05614013,-0.11947346,0.01689252,-0.02243237,-0.00249911,-0.01911305,-0.06009461,0.03571462,-0.00458518,0.0325355,-0.07656146,0.14836511,0.05798574,-0.02279755,0.02855326,-0.0302092,-0.04525108,0.0009844,-0.03584785,-0.03273787,0.03839647,-0.02536405,0.00586857,-0.0361504,0.00609168,-0.05721451,0.08689874,0.04496514,0.031065,0.03445246,0.07799416,0.01550642,-0.00322634,-0.07277595,-0.24234559,-0.05070768,-0.00835736,-0.05040225,-0.01123754,-0.00539672,0.03960456,-0.01796287,0.08637279,0.04710996,0.06163937,0.06483512,-0.0592806,-0.03809784,0.02554749,-0.01220866,0.02392283,-0.03401292,-0.03989825,0.01999409,0.00158604,0.04923389,0.00320841,0.01713825,0.03249551,-0.01524125,0.12345395,0.01530138,0.04042086,-0.01940205,0.03578957,0.06384742,0.0383567,-0.10216243,0.02281629,0.03569599,-0.03613116,-0.03254335,-0.00120739,-0.01421914,-0.02098491,0.02863933,-0.01418965,-0.10451034,-0.00217407,-0.0403419,-0.03305162,0.00433817,0.01972877,0.0690347,-0.01831909,0.00842078,0.02564379,0.02507404,0.0053793,-0.04288164,-0.04170781,-0.00407732,0.01631252,0.04703246,0.02178337,-0.00029439,0.03204021,-0.02259326,0.00008513,-0.04694153,-0.0071984,-0.03421858,0.00100447,-0.0811786,-0.07917376,0.18219733,-0.01128295,-0.00307974,0.03617804,0.00961926,-0.01424468,-0.04170048,0.00436005,-0.00875748,0.05462207,0.05050283,0.03851524,0.02360127,-0.00956821,0.04864152,0.00012721,-0.00838405,0.07019059,-0.05825652,-0.04962061,-0.02105073,-0.09497847,0.00953329,0.0934234,-0.00877478,-0.27259299,-0.0019841,-0.00010528,0.00092034,0.04556349,0.01552157,0.0651544,-0.04878431,-0.04264767,0.03824001,-0.05995236,0.06314716,-0.00641007,-0.05607524,0.01174295,-0.00622507,0.04101006,0.03011033,0.08087381,-0.05829632,0.01607181,0.08886533,0.21173242,0.04754106,0.00706606,0.04340298,0.00404799,0.0928656,0.03263361,0.01839728,-0.00159533,-0.05280515,0.06823917,0.00658982,0.01596972,0.01390558,-0.02237732,-0.02100738,0.04340271,0.01085486,-0.04276317,0.05177785,-0.03932125,0.04596315,0.08098362,0.0003651,-0.03126229,-0.03359874,0.01497547,0.00184759,0.01180836,-0.00163769,0.00806629,-0.01938671,0.01767012,0.0412135,0.00532688,-0.04237905,-0.06649332,0.0036929,0.0195927,0.03014089,0.04507843,0.05906768,0.01544478],"last_embed":{"hash":"59b402b603a02ab09f08ebb3d0f20d07db4508e22015da8743e532572230b90a","tokens":410}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.06383429,0.04537486,-0.00678327,0.00125788,-0.00580676,-0.01901961,0.07449856,-0.02916064,0.01067922,-0.0146783,-0.00146774,-0.02277528,-0.02031565,-0.00825777,-0.05855209,0.04575235,0.00825886,0.0444198,0.04737029,-0.00778642,0.00692275,0.02258787,0.05342903,0.05366869,-0.01311124,-0.01844685,0.00579439,-0.05325744,0.01078833,0.04143485,0.04727944,-0.05625413,0.00376605,-0.02351257,-0.06615897,0.01858215,0.02993876,-0.02366519,-0.04020903,0.02262624,-0.01095759,-0.02082106,-0.04666923,0.01007752,-0.1045737,-0.01860261,0.04354266,-0.01946681,0.00051044,0.01086964,0.01057481,-0.05174249,-0.00447111,0.04521802,0.00653571,0.03073506,-0.03478368,-0.04682375,-0.01659263,-0.00294461,0.01367992,0.04676076,0.07987431,0.01788919,-0.01936861,0.02785863,0.00237864,-0.04458931,0.00123079,-0.01294873,-0.08923823,0.00829877,-0.00744962,0.01856971,0.07016548,-0.03864793,0.0512988,0.00717206,-0.02708644,0.00390315,-0.10702439,0.02382656,-0.00540823,0.04322337,0.03326369,-0.01771878,0.00657728,-0.03034433,0.0395746,0.00339991,0.03490086,0.03551208,0.00122353,0.00513135,0.01354554,-0.07697223,-0.01053537,0.01521866,-0.01160359,-0.0442379,0.0424741,-0.00826448,-0.05098327,-0.01130343,-0.02763559,-0.02088609,0.003158,0.00565592,-0.03330149,-0.00339079,-0.03290503,0.0201,-0.02516446,-0.04858959,-0.08143965,0.03431884,-0.03773126,0.00343413,0.03768906,0.08590557,0.03705208,0.01853135,0.0126287,-0.01484976,-0.0462775,0.00884163,0.00464719,-0.02556306,0.02006271,-0.02968455,-0.01014714,0.00125847,-0.00907931,-0.01731932,-0.01128565,-0.00405758,0.02071997,-0.05264051,0.01786143,-0.03784107,0.03736767,-0.02482439,-0.00493748,-0.0166779,-0.01889956,0.05834084,-0.04149953,-0.05644463,-0.01472861,0.04251026,-0.02359317,-0.0041075,-0.0452735,-0.05254715,0.01944091,0.02086583,0.0322822,0.06559132,0.00933875,-0.00337572,-0.00640054,-0.00328647,0.04704392,0.036047,0.0347277,0.02813948,0.04723056,0.00849545,-0.05945017,-0.00041154,-0.01839285,0.01574331,0.02707038,0.04613394,0.0581153,-0.04594122,0.06914493,0.01806027,-0.05252463,-0.03167415,-0.0083378,-0.03624746,0.00702322,0.0358944,0.01658167,0.02549986,-0.00601871,0.03300253,0.01537167,-0.00472282,0.01971938,-0.00760545,0.0087089,0.00127737,0.05008325,0.01465822,-0.01411953,-0.05236582,-0.02399343,0.02078606,0.02619981,-0.0243276,-0.05329138,-0.04139029,0.04938371,0.01707256,-0.02444063,-0.0173057,-0.01909611,0.02893539,0.05759287,0.01429325,0.02411461,-0.00784329,-0.05858573,0.06070986,-0.06311148,0.09602643,0.01886793,-0.0136369,-0.00681237,-0.1082973,-0.0325588,-0.03020811,0.02113836,-0.06302647,-0.03031398,0.03466285,0.0344496,0.01008982,-0.00898907,0.05406021,-0.02158889,-0.01548349,-0.03630018,-0.00508688,-0.01081614,0.03889184,0.03587064,0.02848458,-0.00923035,-0.00020655,0.00286396,-0.03084283,0.00409552,-0.00241017,-0.0662079,0.0052251,-0.00646996,0.00146621,0.02576336,-0.00822247,0.00647962,0.09016725,0.00928068,-0.03006214,-0.0289761,-0.04685223,0.01207962,-0.02293634,0.0062818,0.04896463,-0.04771726,0.01573032,-0.02582283,-0.03533536,-0.01514525,0.01996413,-0.05660804,0.08055504,-0.03306482,-0.04637984,0.07646023,0.01957966,-0.005414,-0.0034076,-0.06902998,-0.01959863,-0.04573485,-0.03621812,-0.04372697,-0.05062618,0.01860477,0.02752466,-0.07397579,0.03064821,0.00410921,0.0131178,0.05555682,-0.03621111,0.04304884,-0.00336961,-0.05028974,0.04581473,-0.04649062,-0.04086069,0.01743647,0.0385741,0.00453175,-0.05872653,-0.02819139,0.07261318,0.01725761,-0.0278866,0.0087519,0.01511896,0.00438031,0.0153705,-0.01167189,-0.0366062,0.01652249,-0.05029675,0.02285983,0.0159522,0.04189384,0.06343708,0.00449585,-0.01129514,-0.01131399,-0.00379149,-0.04479982,-0.01037863,0.02372965,0.02494011,0.03149789,-0.06375959,0.03944242,-0.00075429,0.02343367,0.04017041,-0.04561311,0.00549574,0.00365827,-0.01235804,0.03385216,-0.01057423,-0.02555863,-0.06814633,0.02813225,-0.0584848,0.0269265,-0.05826256,0.01026581,-0.04147849,-0.01752693,0.01029708,0.00257877,0.02410649,-0.05267528,-0.04276232,-0.01229643,-0.03131955,-0.04415305,0.00275168,0.04048843,-0.0207773,-0.01671316,0.01225006,0.01462551,0.01094926,-0.00465063,-0.04326303,-0.05422603,-0.05213715,-0.04315579,0.02916579,0.08038951,0.03367899,-0.06593248,-0.02222531,0.0288453,0.02533814,-0.0455042,-0.00824455,0.05120368,-0.00846196,0.01220138,-0.0011848,0.0317282,-0.01768681,0.01260712,-0.02708264,-0.02764937,-0.02796922,-0.1082244,-0.04053737,0.04218012,-0.04152314,-0.02698733,-0.05275648,-0.06179675,0.01308599,-0.04279248,0.0502181,0.01007311,0.01038617,0.04733428,-0.02052441,-0.02337616,-0.013337,0.02870751,0.03347923,0.00234182,0.0155189,-0.01006173,0.06226338,-0.0194679,0.06801362,-0.0197925,-0.02895864,0.00232145,0.04449383,-0.00762067,-0.00283645,0.0099974,0.02704902,-0.01514632,-0.03888796,0.02292273,0.05803626,0.01466346,-0.04316358,0.04053292,0.03746004,0.02855438,-0.0312193,-0.06409596,0.03348916,0.00354004,0.01352032,-0.02114595,0.02891782,-0.09531904,-0.0614101,-0.02813923,-0.02600897,0.08475368,0.02881219,0.01130145,0.01628313,-0.03574874,-0.01887334,-0.01058509,-0.03634458,0.06855742,0.05515296,-0.01274371,-0.02332108,-0.00238749,0.07503348,-0.01011685,-0.04279277,-0.06638024,-0.03637231,0.03419951,0.01512395,0.02000431,-0.04773981,-0.01386196,-0.00624908,-0.06459857,-0.02031661,0.04875203,-0.01130122,0.01722641,-0.00883676,0.01260774,-0.03351503,-0.05806015,0.00000582,0.00884117,-0.02802269,-0.00170385,-0.01551675,0.08056215,0.03664528,-0.03079687,-0.01002141,-0.06237046,-0.02701854,-0.00885426,0.04046333,0.02334624,0.01502761,-0.00627957,0.05121367,-0.02926062,-0.00331698,0.01367389,-0.01782833,-0.0289199,0.07362521,0.04037573,0.00992276,0.01693219,-0.01093109,0.01282365,0.05197503,-0.0037298,0.04631373,0.01248683,0.04968121,-0.03253272,-0.00794377,0.05476902,0.04322629,-0.03972765,0.01905155,0.06182313,-0.02279073,-0.0239633,-0.08843303,0.08091296,0.0243528,-0.02813803,-0.05533443,-0.03160403,0.02488623,-0.02884126,0.00619133,0.00224667,0.01636133,-0.06744885,-0.00157979,0.0374563,-0.02164109,-0.0303858,0.07566209,0.04805396,0.03641011,-0.03815013,0.00022287,0.01053731,-0.01737146,0.03153645,-0.04660669,0.04963924,-0.00896178,-0.01676884,0.05065184,0.09178942,0.02781933,0.0093857,0.02116535,-0.02304967,-0.00036524,0.03167745,-0.00933334,0.0634032,0.00517076,0.00021766,0.04872344,0.02716522,-0.01265641,-0.01968911,0.01745578,0.01193419,-0.03060278,-0.03841199,0.05722049,0.00391569,0.04429422,-0.03065792,0.01901426,-0.00438217,-0.00595183,-0.0486226,0.01720248,0.01187035,0.03296534,-0.04616084,-0.00221912,-0.00736146,0.02904395,-0.06176984,-0.02618139,0.00799543,0.00222206,0.05226148,-0.08374602,0.02838264,-0.01292009,0.0538209,-0.00897842,-0.01405313,0.01220214,0.00989428,0.05098023,0.01771318,0.00703264,-0.00325938,-0.05183677,-0.0256983,-0.01829835,-0.02135431,0.04475721,-0.00978012,0.02361716,0.03514566,-0.03862298,0.01225053,-0.02728861,-0.00989768,-0.00076341,0.00221872,-0.08123224,-0.0269461,-0.07292279,-0.00137324,0.03923481,0.00179049,0.05582665,0.02003729,0.01971594,0.00933874,-0.05062337,-0.00268568,-0.01366203,-0.00840593,-0.04095765,-0.0049109,-0.04448497,-0.02737814,-0.04240055,-0.02059075,0.01231009,0.01150365,0.00469331,0.0524857,-0.05622553,-0.06924055,-0.02596866,0.04398196,0.02910957,-0.00897418,0.03799327,-0.07838728,-0.05381851,-0.03908759,-0.03837175,0.01149461,0.02510838,0.00455779,0.04955727,0.01979543,-0.02299243,-0.00974193,0.00690699,0.03184395,-0.02349779,-0.00318958,-0.01047604,-0.01314198,-0.01013892,-0.04650513,0.04514224,-0.03447508,0.01953012,-0.00246872,0.04516698,-0.0517557,-0.02177794,-0.00908784,-0.02726282,-0.05825712,0.05505252,0.03910954,0.01821785,-0.0349356,-0.00190231,-0.09349077,0.00389688,-0.02000664,0.01189911,0.01755699,0.04015215,0.02271315,0.03181181,0.01578549,-0.02916392,-0.09542488,0.05524086,-0.00855039,-0.01624698,0.05411326,-0.01961778,0.02460086,0.01870694,-0.04678892,-0.00248202,-0.02662402,0.02367297,-0.01885799,-0.01401633,0.06711594,-0.04587944,0.06360637,-0.03792476,0.02148128,0.04094121,-0.01736198,0.0258382,-0.02761413,0.02061764,-0.0207151,0.00241296,0.02154309,0.00944979,0.10942987,-0.0439827,-0.06463076,-0.00626302,-0.02028777,0.03557714,0.01032982,-0.03621005,-0.00489846,0.02810644,-0.04522432,-0.00179067,-0.03629046,-0.051854,0.06355766,0.03090702,-0.00784749,-0.00949728,-0.02527255,-0.00594411,0.00816494,-0.00506895,-0.04231322,-0.09830385,0.00287073,0.05127282,0.03962668,0.00727975,-0.03730368,-0.00482021,0.04894612,-0.01040311,-0.0092984,0.00649252,-0.03176745,0.00509159,-0.00569395,0.05877437,-0.01124617,0.00634439,0.00094966,0.03327291,0.00108347,0.03216354,-0.01127134,0.05866431,0.02112348,0.02840799,-0.04976097,-0.0256695,0.00000114,-0.00773032,-0.02930156,0.02412826,-0.00230821,-0.01995166,-0.01220732,-0.04643224,-0.05369934,0.04388007],"last_embed":{"tokens":1292,"hash":"1glvme0"}}},"last_read":{"hash":"1glvme0","at":1751335173143},"class_name":"SmartSource","outlinks":[{"title":"Jenkins","target":"Jenkins","line":11},{"title":"反序列化","target":"反序列化","line":12},{"title":"反序列化","target":"反序列化","line":12},{"title":"反序列化","target":"反序列化","line":13},{"title":"session","target":"session","line":30},{"title":"token","target":"token","line":30},{"title":"反序列化","target":"反序列化","line":31},{"title":"序列化","target":"序列化","line":31},{"title":"json","target":"json","line":32},{"title":"序列化","target":"序列化","line":33},{"title":"json","target":"json","line":34},{"title":"序列化","target":"序列化","line":36},{"title":"Java","target":"Java","line":39},{"title":"反序列化","target":"反序列化","line":44},{"title":"序列化","target":"序列化","line":45},{"title":"反序列化","target":"反序列化","line":48},{"title":"PHP","target":"PHP","line":56},{"title":"Python","target":"Python","line":56},{"title":"序列化","target":"序列化","line":60},{"title":"反序列化","target":"反序列化","line":61},{"title":"#Python的反序列化实验","target":"#Python的反序列化实验","line":62},{"title":"反射（reflection）机制","target":"https://xz.aliyun.com/t/7029/","line":64},{"title":"Java","target":"Java","line":64},{"title":"变量","target":"变量","line":67},{"title":"Java","target":"Java","line":69},{"title":"序列化","target":"序列化","line":95},{"title":"序列化","target":"序列化","line":125}],"metadata":{"tags":["网络安全/漏洞/Web安全"],"英文":"Deserialization Vulnerability","aliases":["Deserialization Vulnerability"],"cssclasses":["editor-full"]},"blocks":{"#---frontmatter---":[1,9],"#简介":[10,15],"#简介#{1}":[11,15],"#相关漏洞":[16,21],"#相关漏洞#{1}":[17,17],"#相关漏洞#{2}":[18,20],"#相关漏洞#{3}":[21,21],"#常用工具":[22,26],"#常用工具#{1}":[23,23],"#常用工具#{2}":[24,24],"#常用工具#{3}":[25,25],"#常用工具#{4}":[26,26],"#漏洞环境":[27,73],"#漏洞环境#可能出现的位置":[29,41],"#漏洞环境#可能出现的位置#{1}":[30,30],"#漏洞环境#可能出现的位置#{2}":[31,32],"#漏洞环境#可能出现的位置#{3}":[33,34],"#漏洞环境#可能出现的位置#{4}":[35,35],"#漏洞环境#可能出现的位置#{5}":[36,36],"#漏洞环境#可能出现的位置#{6}":[37,38],"#漏洞环境#可能出现的位置#{7}":[39,40],"#漏洞环境#可能出现的位置#{8}":[41,41],"#漏洞环境#漏洞成因/环节":[42,54],"#漏洞环境#漏洞成因/环节#{1}":[43,49],"#漏洞环境#漏洞成因/环节#{2}":[50,50],"#漏洞环境#漏洞成因/环节#{3}":[51,53],"#漏洞环境#漏洞成因/环节#{4}":[54,54],"#漏洞环境#漏洞原理":[55,73],"#漏洞环境#漏洞原理#{1}":[56,61],"#漏洞环境#漏洞原理#{2}":[62,62],"#漏洞环境#漏洞原理#{3}":[63,63],"#漏洞环境#漏洞原理#{4}":[64,71],"#漏洞环境#漏洞原理#{5}":[72,73],"#实际测试":[74,137],"#实际测试#Python的反序列化实验":[76,137],"#实际测试#Python的反序列化实验#序列化与反序列化":[78,100],"#实际测试#Python的反序列化实验#序列化与反序列化#{1}":[79,94],"#实际测试#Python的反序列化实验#序列化与反序列化#{2}":[95,96],"#实际测试#Python的反序列化实验#序列化与反序列化#{3}":[97,99],"#实际测试#Python的反序列化实验#序列化与反序列化#{4}":[100,100],"#实际测试#Python的反序列化实验#系统命令序列化传输":[101,137],"#实际测试#Python的反序列化实验#系统命令序列化传输#{1}":[102,116],"#实际测试#Python的反序列化实验#系统命令序列化传输#{2}":[117,117],"#实际测试#Python的反序列化实验#系统命令序列化传输#{3}":[118,123],"#实际测试#Python的反序列化实验#系统命令序列化传输#{4}":[124,125],"#实际测试#Python的反序列化实验#系统命令序列化传输#{5}":[126,134],"#实际测试#Python的反序列化实验#系统命令序列化传输#{6}":[135,137]},"last_import":{"mtime":1744268861891,"size":4991,"at":1748488129023,"hash":"1glvme0"},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/反序列化漏洞.md","last_embed":{"hash":"1glvme0","at":1751335173143}},