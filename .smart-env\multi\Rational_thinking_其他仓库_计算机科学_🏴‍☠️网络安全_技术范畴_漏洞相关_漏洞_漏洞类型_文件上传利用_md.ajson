"smart_sources:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/文件上传利用.md": {"path":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/文件上传利用.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09688119,-0.01739952,-0.01707139,-0.04602311,0.04866718,-0.0112848,-0.03525395,0.00647652,0.03765734,0.00031405,0.04335378,-0.04052106,0.05612594,0.04321773,0.05207736,0.02937032,0.02475648,-0.02598117,-0.0366011,0.00771348,0.08001227,-0.04699066,0.00333688,-0.05479203,0.00441737,-0.01054188,0.00686204,-0.0062618,-0.02327386,-0.15156984,-0.00732665,-0.01986939,0.02819213,0.00944833,-0.02004937,-0.05522319,0.04725646,0.05115266,0.0089892,-0.00781339,-0.0136631,0.05195465,0.00752944,-0.03919458,-0.0377111,-0.03279465,-0.05585916,-0.02898126,0.02167976,-0.04024228,-0.04735774,-0.04725954,-0.04178105,-0.02398483,-0.05956518,-0.02564728,0.00255294,-0.00146777,0.03047976,0.01812726,0.01887946,0.02604589,-0.20138383,0.06667764,0.02364883,-0.04019455,-0.04591884,0.0015447,0.0131695,0.0593482,-0.09866584,0.04839836,-0.01905876,0.07032742,0.06059029,-0.04081381,0.04777976,-0.02288367,-0.0592458,-0.05695625,-0.02638469,0.0461727,-0.04285726,0.0086402,-0.00552393,0.02341309,-0.01425257,-0.02218796,-0.03937772,-0.00153075,0.00124128,-0.02860854,-0.01863693,0.03229662,-0.0002293,-0.00006323,0.01506983,0.06238334,-0.05443395,0.11569817,-0.09043457,-0.00071118,0.00150659,-0.06835896,-0.01262173,-0.02927128,0.00143199,-0.01616395,0.03090797,-0.01201197,-0.04776692,-0.03303134,0.05157764,-0.02886489,0.05114781,0.04199591,0.00157741,-0.00431122,-0.03477864,-0.04645275,-0.014527,0.00071385,0.08001847,-0.0243599,-0.04096747,-0.02667906,0.06358313,0.05583274,0.03166779,0.03274944,0.06320789,-0.00007844,-0.06854869,-0.0447047,-0.01379241,-0.00485578,-0.03570165,0.01780573,-0.02683859,-0.03134228,-0.01851305,-0.06821196,-0.00850182,-0.0969476,-0.08223458,0.08299218,-0.05742368,-0.02599519,0.0728813,-0.0534361,0.0005791,0.09323135,-0.03571081,-0.01339372,-0.00634646,-0.00755015,0.0871339,0.12870216,-0.03703646,-0.019045,-0.00421561,0.02668517,-0.09150268,0.1720399,0.00875004,-0.07864094,-0.02322538,-0.00655393,0.0394078,-0.02088221,0.03298344,-0.01587305,0.04192702,-0.00315648,0.07051121,-0.00708674,0.00345421,-0.05243384,0.02506807,0.04025916,0.09976698,-0.02094716,-0.08021198,0.07543197,-0.01661422,-0.07981157,-0.06113423,-0.00584915,0.02454254,-0.02597666,-0.1051896,0.05628638,-0.02981035,-0.02041492,-0.05311572,-0.06311572,0.03261444,0.01746189,0.02190295,-0.07835595,0.10111682,0.03395945,-0.01546569,0.02149491,-0.01695247,-0.01314809,0.01200745,-0.00348422,0.02007066,0.00962438,0.01417276,-0.02139618,-0.01101915,-0.00638705,-0.02766009,0.05501667,0.0286551,0.00043858,0.02960491,0.08139591,-0.00223901,-0.06402975,-0.04029927,-0.19617094,-0.00721419,0.00104016,-0.03962103,0.00388903,-0.00920956,0.04191436,0.03379004,0.09398349,0.10055803,0.06544598,0.01381618,-0.09428155,-0.02680024,0.00692676,-0.02467266,0.01739382,-0.01277689,-0.0272307,0.00968913,0.00819396,0.04488914,-0.02409378,-0.01990427,0.07944435,-0.03319791,0.12488505,0.05593228,0.0112152,0.04004519,0.05964489,0.05660033,-0.00427687,-0.11064101,0.03691287,0.00768568,-0.03611472,-0.04000525,-0.03003523,-0.02396066,0.03076147,0.00827864,-0.02687527,-0.07119375,-0.01941498,-0.01898451,-0.01812189,0.01071914,0.03714346,0.02747493,0.00124994,-0.00708497,0.01024581,0.00555062,-0.03642927,-0.05404433,-0.03783742,0.00234136,-0.01546572,0.01220526,0.06908478,-0.00652116,0.05247821,0.02317799,-0.01598392,-0.02752782,-0.01107706,-0.02530865,-0.07177649,-0.001571,0.0063017,0.14904231,-0.01619285,-0.0232436,0.03254563,0.01907558,0.00571604,-0.00898249,0.00963883,-0.00903223,0.02949267,0.03452015,0.04062585,0.02472722,-0.02339474,0.02360387,0.01637513,-0.02253763,0.08100039,-0.04654332,-0.04129114,-0.01044452,-0.05258643,0.01915523,0.07817937,-0.01685204,-0.31424063,0.01943025,-0.00951124,0.01751019,0.05902088,0.0242616,0.06582349,0.03851939,-0.01550218,0.03657699,-0.0678674,0.04116604,-0.01935476,-0.0440522,-0.02332848,-0.03899636,0.03329122,0.01975816,0.08959079,-0.00583995,0.03002567,0.05053617,0.21987031,-0.00064133,0.04544404,-0.00415322,-0.00243107,0.04910018,0.02199192,0.02926254,0.02134043,-0.04318424,0.04215684,-0.0336097,0.05292471,0.03837605,-0.04983968,0.03858454,0.00557319,0.0168741,-0.03532891,0.04346908,-0.08087593,0.02946321,0.07196455,0.03468477,-0.01391089,-0.0219962,-0.01652893,0.06092586,-0.00352468,0.00762711,0.00379351,-0.0154131,0.01979197,0.03004391,0.03336873,-0.04115589,-0.06211543,-0.00760002,0.04734345,0.01705403,0.10021437,0.12757289,0.08263509],"last_embed":{"hash":"0c3dd77631514e6dcd620057b55f3620267dcb89edc4aa9e2f8ac53cd08aa707","tokens":416}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.06620894,0.00326759,-0.02303442,-0.02650644,0.01050505,-0.02880851,0.06341499,0.01472531,-0.02789849,-0.03949224,-0.04626093,-0.02340641,-0.08982363,-0.05029405,0.05863907,0.07098556,0.00655692,0.01479365,0.06073847,-0.03843066,-0.02353115,0.04872445,0.05936811,-0.00788833,-0.02076813,-0.00336905,0.00299403,-0.02798456,0.033749,0.07426228,0.08289128,-0.0254679,0.02507464,0.01229717,-0.06065508,-0.05693259,0.02324891,-0.0369069,-0.00168895,0.01779027,-0.03399306,0.01139956,-0.00551952,0.03988754,-0.05106027,-0.01432054,-0.03054371,-0.03428975,-0.00709044,-0.0015502,0.03763823,0.02254036,-0.0140441,0.01657382,0.01011149,0.04769878,-0.0337762,-0.04766745,-0.00072442,-0.00596796,0.02921934,-0.04663434,0.06823432,0.03493899,0.02352824,-0.004534,-0.0190612,-0.00590464,-0.01401434,-0.03142304,-0.05540223,-0.03507837,0.00199246,0.04667316,0.06767285,-0.01169914,0.06441215,0.00045321,0.04983227,0.02600385,-0.06452461,0.0206857,-0.01776822,0.01956704,-0.01179529,0.00989777,0.09411725,-0.06506698,0.04710499,-0.00103741,-0.00608722,0.00419441,-0.02259861,-0.05930649,0.03969489,-0.02163839,0.05659033,-0.01273701,0.01520677,-0.01549901,-0.01217968,-0.00106054,-0.06798652,-0.00077662,-0.03374651,-0.09692119,0.00750915,0.05165426,-0.02689898,0.00377373,0.00831829,0.01362397,-0.02823772,0.02967915,0.0237329,0.04870693,-0.03749943,0.00662268,0.00443864,0.0509938,0.01242187,0.04197713,0.03327071,-0.04323447,-0.04190759,0.00011737,-0.03946587,-0.02878984,0.04010262,0.02688407,-0.01747625,0.05203167,-0.03506871,-0.02681347,0.05464311,0.01756844,0.02680833,-0.03845859,0.0631571,-0.04649099,0.01945476,-0.02074465,-0.0253612,0.00645768,-0.03941355,0.01311274,-0.02489233,-0.06208543,-0.04878734,0.04815702,0.06184999,0.0251909,-0.0390011,-0.04486383,-0.00494435,-0.01443651,-0.00054776,0.01144305,0.01767275,-0.0114767,-0.00387797,0.03724797,-0.00155349,-0.00140695,-0.01014028,-0.00687012,0.0127144,0.0440762,0.02047694,0.02499542,0.00818187,0.02648512,-0.00848681,0.0204653,0.01904983,-0.06482049,0.05814113,0.05298011,-0.03832422,-0.02444279,0.03166022,-0.05735291,-0.01111683,0.0559812,0.01642847,0.02538118,-0.03880483,0.02274861,0.01163855,0.01055016,0.05137399,0.03442612,0.01855289,0.03104397,-0.01614903,0.02008475,0.02272015,-0.08886264,-0.02564213,0.03580113,0.02092232,-0.00158746,-0.0258083,-0.08500981,0.01507754,-0.02420115,-0.01829755,-0.00472168,-0.03784197,0.06965253,-0.04711403,0.00241397,0.01124645,0.01511998,-0.0370261,0.03474586,-0.0169837,-0.00185401,0.01578887,-0.01530454,0.00949906,-0.04328725,-0.07186638,-0.02169424,-0.05224377,-0.02260692,-0.01029212,0.00126926,0.03524486,0.00891828,-0.04229855,0.00600072,-0.02185582,-0.00051639,-0.01659292,-0.01579492,0.0619919,0.01819493,0.01251017,0.00427583,0.01198801,0.04445605,0.02627408,-0.00265826,-0.02590395,-0.03116802,-0.04950477,0.04186272,0.01541138,0.01931635,0.01690325,0.02688808,-0.00889467,0.04611053,-0.00576897,-0.07803577,-0.02007153,-0.00558883,-0.0044121,-0.03420746,0.03158192,-0.00916528,-0.06458268,0.01659974,-0.02426492,-0.04174457,-0.01840894,-0.00232214,-0.04826044,0.05177544,-0.02786164,-0.03303047,0.09732958,-0.03409807,-0.01655903,0.01166748,0.00418011,0.00023794,-0.03358697,-0.01025554,-0.03128577,0.0004525,0.00604948,-0.0028907,-0.07223164,0.06397529,0.0165896,-0.04258748,0.04132665,-0.01210732,0.01823895,-0.01627601,-0.0392289,0.05048275,-0.05662559,-0.04038417,-0.01361649,0.04441874,-0.01420367,-0.04377936,0.02332652,0.07367889,0.00576021,-0.0431382,0.00825136,0.01641075,0.00403403,0.04362015,-0.02512856,-0.04056832,0.03542723,-0.05649866,0.04562966,0.03253547,0.05335804,0.06021814,0.0089834,-0.02819394,-0.05055751,0.0375957,-0.0757889,0.00089219,-0.01991464,0.00014619,0.01714349,-0.05776855,0.0166604,0.00275057,0.08875599,0.0216938,-0.004307,-0.00222025,0.01835435,-0.01191992,-0.00505374,-0.0376574,-0.0248566,-0.03448436,0.00873071,-0.00918021,0.00670713,-0.03690407,-0.00068807,-0.08731282,-0.02811189,0.02826205,-0.01369059,0.00012264,-0.02514151,-0.08525455,0.03875915,0.01120675,-0.02543746,0.02187977,0.04421295,-0.03367951,-0.05312986,-0.03210794,0.00030674,0.01424371,-0.02911039,-0.03402749,-0.01456375,-0.04714186,-0.0014198,0.01422704,0.05483298,0.0294992,-0.00138424,-0.06285494,0.01834605,-0.00876392,-0.02589634,-0.01775119,0.01458451,-0.01056005,-0.02677014,0.01667698,0.06276949,-0.02614606,-0.02733807,-0.03295276,0.03691865,-0.02717428,-0.03619988,-0.03338031,0.03324534,-0.01583773,-0.09349804,-0.08592784,-0.03525915,0.02096689,-0.04339195,0.03760728,0.03292758,-0.03480513,0.03145624,0.01555793,-0.02349371,-0.04432684,0.01216982,-0.04288609,0.06258869,0.00545548,0.00193215,0.06127199,-0.05916303,0.02880013,-0.05719955,-0.03309139,0.01451104,0.0171369,0.03687897,0.04021581,0.04860065,0.01706123,0.00511572,-0.07993206,0.00064713,0.03936596,0.01465711,-0.01287088,0.02590942,0.06207989,-0.01370154,-0.01547552,-0.03863194,0.00639562,-0.05448926,0.02402917,-0.03328552,-0.00605358,-0.02090931,0.01928427,-0.00831862,-0.04434728,0.04574257,0.01312182,0.06626286,0.0211526,-0.05957536,0.02177811,-0.02888769,0.03500619,0.07505486,0.0989539,-0.0296052,-0.07627548,-0.00819735,-0.00717101,-0.0175407,-0.02700595,-0.04677552,0.00325218,0.00288394,-0.01268041,0.02033329,-0.04086858,0.02364162,-0.02072959,-0.02872069,-0.04611912,0.03017832,0.01418402,0.02148969,0.06558903,-0.00064831,0.01243101,-0.02884616,-0.00123617,-0.02307404,0.01628562,-0.00290667,-0.02100261,0.02633009,-0.00037507,0.03014614,0.00434089,-0.0655042,0.02602188,-0.04512271,0.02983746,-0.02961746,-0.02978213,-0.03022012,-0.00203914,0.00669216,0.01016359,0.0324913,-0.01936939,-0.01572529,0.04197613,0.03738278,0.02002344,0.01385719,-0.02954032,0.01065721,0.05610045,-0.00516863,0.07582401,0.00719535,0.03285611,0.01095455,0.04092118,-0.01380678,0.02286714,-0.02855385,0.01012114,0.03150434,-0.0002753,-0.02604481,-0.09890568,0.04381373,0.07184708,-0.0318989,-0.03787295,0.01929129,-0.01767286,0.00062557,-0.0359734,-0.02958956,0.03441362,-0.04063508,-0.02180664,-0.00832878,-0.00544052,-0.00555163,0.06593812,0.06506563,-0.02588288,0.00996002,0.04625165,0.01284462,-0.01538544,0.04121189,-0.04567828,0.04506957,-0.01772629,-0.03211623,0.01140277,0.0677497,0.03438229,0.01707154,0.02763828,-0.03492663,-0.04676417,0.03197512,-0.00172246,0.04887424,-0.0524418,0.01606796,0.02497708,-0.0054245,-0.02743732,-0.07091142,0.01156796,0.00089706,-0.02574573,-0.01505567,0.02699706,-0.01980722,0.052536,-0.02187509,-0.01904058,-0.05549416,-0.01946766,-0.05180006,0.0164903,0.01689949,0.01648023,-0.09756605,-0.01084936,0.03807154,0.00484188,-0.02946355,-0.03476829,0.00462161,-0.03117096,0.02892413,-0.03342757,-0.04824837,0.01006283,0.02903934,0.02843794,-0.01542134,-0.02191584,0.03173934,-0.01480269,0.02924301,-0.01321731,-0.01491,-0.01268775,0.03503746,-0.00821741,-0.05396624,-0.03917801,-0.00959163,0.01792672,-0.01206789,-0.00796804,-0.0071696,-0.0220663,0.01731068,-0.00592993,0.01906813,-0.08865133,-0.02271549,-0.06377423,0.04265276,0.08053382,-0.0022177,0.01719395,0.02615761,0.00792039,0.02547762,-0.06445371,-0.00086123,0.03072441,-0.02688897,-0.01605125,-0.02788184,-0.09506471,-0.05411024,-0.00397964,0.04802596,0.05087764,-0.00098613,0.02057376,0.08793311,-0.05054531,-0.09650429,-0.03822151,-0.02206879,0.02398345,0.01024239,0.05225046,-0.00106147,-0.00882596,-0.01003985,-0.00841794,0.02099377,-0.01369738,-0.01860981,-0.01498105,0.03085916,-0.08534592,-0.02862865,-0.04636876,0.06977174,-0.01447339,0.01203201,-0.02854874,0.00090042,0.01169874,-0.03673618,0.05426349,-0.03024265,0.00596546,0.01559663,0.02206914,-0.0039474,-0.03782445,-0.04243582,-0.0097672,-0.03300242,0.02579951,-0.0177588,0.01847925,-0.04751519,0.03516046,-0.04499974,0.03940692,-0.04491671,0.01570873,-0.03158403,0.03955757,0.01885579,0.0559152,-0.04081559,-0.02844593,-0.05718825,0.00672831,0.01086227,0.01838197,0.02513747,-0.03086425,-0.00255312,0.00382201,0.01084142,-0.00099809,-0.03675716,0.03680068,0.00222067,0.01796487,-0.04388127,-0.04729309,0.02817605,-0.07122406,-0.00493163,-0.00523768,-0.02936574,0.00895235,0.00683268,0.01807356,-0.03595877,-0.03543453,0.01514775,0.01476613,0.04352697,0.01859037,-0.05498433,0.05535777,-0.01186999,0.00870773,0.00133164,-0.0394052,-0.02669134,0.07292057,-0.04101872,0.00316006,0.01848444,0.01634534,0.00599303,0.0155405,-0.00201012,-0.03960577,0.00499093,0.00149979,0.00547553,0.02106011,-0.01883366,-0.0690629,0.00714382,0.05618105,0.04273354,0.0014213,0.04573914,-0.00501766,0.0560451,0.00600395,0.06088787,0.00294124,-0.03515238,0.03599898,-0.03235586,0.07224993,0.01488444,0.00032224,0.03600146,0.01083716,0.04855766,0.06052323,0.01230455,0.10598227,-0.00746199,0.06920496,-0.00413897,-0.05367523,0.00000102,-0.04284136,0.03936649,0.06005852,-0.01661418,-0.00640535,-0.01412977,-0.05855663,-0.00530611,-0.01005334],"last_embed":{"tokens":253,"hash":"eypkxl"}}},"last_read":{"hash":"eypkxl","at":1751268683100},"class_name":"SmartSource","outlinks":[],"metadata":{"aliases":["file upload","文件上传漏洞"],"tags":["网络安全/漏洞/文件上传"],"类型":["漏洞"],"文档更新日期":"2023-12-20"},"blocks":{"#---frontmatter---":[1,10],"#简介":[11,20],"#简介#{1}":[12,13],"#简介#{2}":[14,15],"#简介#{3}":[16,19],"#简介#{4}":[20,20],"#漏洞形成的原因":[21,37],"#漏洞形成的原因#{1}":[23,23],"#漏洞形成的原因#{2}":[24,24],"#漏洞形成的原因#{3}":[25,25],"#漏洞形成的原因#{4}":[26,28],"#漏洞形成的原因#{5}":[29,33],"#漏洞形成的原因#{6}":[34,37]},"last_import":{"mtime":1715930118181,"size":1046,"at":1749024987637,"hash":"eypkxl"},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/文件上传利用.md","last_embed":{"hash":"eypkxl","at":1751268683100}},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/文件上传利用.md#---frontmatter---": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/文件上传利用.md#---frontmatter---","lines":[1,10],"size":96,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/文件上传利用.md#简介": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/文件上传利用.md#简介","lines":[11,20],"size":189,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/文件上传利用.md#简介#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/文件上传利用.md#简介#{1}","lines":[12,13],"size":68,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/文件上传利用.md#简介#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/文件上传利用.md#简介#{2}","lines":[14,15],"size":46,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/文件上传利用.md#简介#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/文件上传利用.md#简介#{3}","lines":[16,19],"size":64,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/文件上传利用.md#简介#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/文件上传利用.md#简介#{4}","lines":[20,20],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/文件上传利用.md#漏洞形成的原因": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/文件上传利用.md#漏洞形成的原因","lines":[21,37],"size":191,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/文件上传利用.md#漏洞形成的原因#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/文件上传利用.md#漏洞形成的原因#{1}","lines":[23,23],"size":14,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/文件上传利用.md#漏洞形成的原因#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/文件上传利用.md#漏洞形成的原因#{2}","lines":[24,24],"size":10,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/文件上传利用.md#漏洞形成的原因#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/文件上传利用.md#漏洞形成的原因#{3}","lines":[25,25],"size":12,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/文件上传利用.md#漏洞形成的原因#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/文件上传利用.md#漏洞形成的原因#{4}","lines":[26,28],"size":53,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/文件上传利用.md#漏洞形成的原因#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/文件上传利用.md#漏洞形成的原因#{5}","lines":[29,33],"size":79,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/文件上传利用.md#漏洞形成的原因#{6}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/文件上传利用.md#漏洞形成的原因#{6}","lines":[34,37],"size":7,"outlinks":[],"class_name":"SmartBlock"},
"smart_sources:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/文件上传利用.md": {"path":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/文件上传利用.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09688119,-0.01739952,-0.01707139,-0.04602311,0.04866718,-0.0112848,-0.03525395,0.00647652,0.03765734,0.00031405,0.04335378,-0.04052106,0.05612594,0.04321773,0.05207736,0.02937032,0.02475648,-0.02598117,-0.0366011,0.00771348,0.08001227,-0.04699066,0.00333688,-0.05479203,0.00441737,-0.01054188,0.00686204,-0.0062618,-0.02327386,-0.15156984,-0.00732665,-0.01986939,0.02819213,0.00944833,-0.02004937,-0.05522319,0.04725646,0.05115266,0.0089892,-0.00781339,-0.0136631,0.05195465,0.00752944,-0.03919458,-0.0377111,-0.03279465,-0.05585916,-0.02898126,0.02167976,-0.04024228,-0.04735774,-0.04725954,-0.04178105,-0.02398483,-0.05956518,-0.02564728,0.00255294,-0.00146777,0.03047976,0.01812726,0.01887946,0.02604589,-0.20138383,0.06667764,0.02364883,-0.04019455,-0.04591884,0.0015447,0.0131695,0.0593482,-0.09866584,0.04839836,-0.01905876,0.07032742,0.06059029,-0.04081381,0.04777976,-0.02288367,-0.0592458,-0.05695625,-0.02638469,0.0461727,-0.04285726,0.0086402,-0.00552393,0.02341309,-0.01425257,-0.02218796,-0.03937772,-0.00153075,0.00124128,-0.02860854,-0.01863693,0.03229662,-0.0002293,-0.00006323,0.01506983,0.06238334,-0.05443395,0.11569817,-0.09043457,-0.00071118,0.00150659,-0.06835896,-0.01262173,-0.02927128,0.00143199,-0.01616395,0.03090797,-0.01201197,-0.04776692,-0.03303134,0.05157764,-0.02886489,0.05114781,0.04199591,0.00157741,-0.00431122,-0.03477864,-0.04645275,-0.014527,0.00071385,0.08001847,-0.0243599,-0.04096747,-0.02667906,0.06358313,0.05583274,0.03166779,0.03274944,0.06320789,-0.00007844,-0.06854869,-0.0447047,-0.01379241,-0.00485578,-0.03570165,0.01780573,-0.02683859,-0.03134228,-0.01851305,-0.06821196,-0.00850182,-0.0969476,-0.08223458,0.08299218,-0.05742368,-0.02599519,0.0728813,-0.0534361,0.0005791,0.09323135,-0.03571081,-0.01339372,-0.00634646,-0.00755015,0.0871339,0.12870216,-0.03703646,-0.019045,-0.00421561,0.02668517,-0.09150268,0.1720399,0.00875004,-0.07864094,-0.02322538,-0.00655393,0.0394078,-0.02088221,0.03298344,-0.01587305,0.04192702,-0.00315648,0.07051121,-0.00708674,0.00345421,-0.05243384,0.02506807,0.04025916,0.09976698,-0.02094716,-0.08021198,0.07543197,-0.01661422,-0.07981157,-0.06113423,-0.00584915,0.02454254,-0.02597666,-0.1051896,0.05628638,-0.02981035,-0.02041492,-0.05311572,-0.06311572,0.03261444,0.01746189,0.02190295,-0.07835595,0.10111682,0.03395945,-0.01546569,0.02149491,-0.01695247,-0.01314809,0.01200745,-0.00348422,0.02007066,0.00962438,0.01417276,-0.02139618,-0.01101915,-0.00638705,-0.02766009,0.05501667,0.0286551,0.00043858,0.02960491,0.08139591,-0.00223901,-0.06402975,-0.04029927,-0.19617094,-0.00721419,0.00104016,-0.03962103,0.00388903,-0.00920956,0.04191436,0.03379004,0.09398349,0.10055803,0.06544598,0.01381618,-0.09428155,-0.02680024,0.00692676,-0.02467266,0.01739382,-0.01277689,-0.0272307,0.00968913,0.00819396,0.04488914,-0.02409378,-0.01990427,0.07944435,-0.03319791,0.12488505,0.05593228,0.0112152,0.04004519,0.05964489,0.05660033,-0.00427687,-0.11064101,0.03691287,0.00768568,-0.03611472,-0.04000525,-0.03003523,-0.02396066,0.03076147,0.00827864,-0.02687527,-0.07119375,-0.01941498,-0.01898451,-0.01812189,0.01071914,0.03714346,0.02747493,0.00124994,-0.00708497,0.01024581,0.00555062,-0.03642927,-0.05404433,-0.03783742,0.00234136,-0.01546572,0.01220526,0.06908478,-0.00652116,0.05247821,0.02317799,-0.01598392,-0.02752782,-0.01107706,-0.02530865,-0.07177649,-0.001571,0.0063017,0.14904231,-0.01619285,-0.0232436,0.03254563,0.01907558,0.00571604,-0.00898249,0.00963883,-0.00903223,0.02949267,0.03452015,0.04062585,0.02472722,-0.02339474,0.02360387,0.01637513,-0.02253763,0.08100039,-0.04654332,-0.04129114,-0.01044452,-0.05258643,0.01915523,0.07817937,-0.01685204,-0.31424063,0.01943025,-0.00951124,0.01751019,0.05902088,0.0242616,0.06582349,0.03851939,-0.01550218,0.03657699,-0.0678674,0.04116604,-0.01935476,-0.0440522,-0.02332848,-0.03899636,0.03329122,0.01975816,0.08959079,-0.00583995,0.03002567,0.05053617,0.21987031,-0.00064133,0.04544404,-0.00415322,-0.00243107,0.04910018,0.02199192,0.02926254,0.02134043,-0.04318424,0.04215684,-0.0336097,0.05292471,0.03837605,-0.04983968,0.03858454,0.00557319,0.0168741,-0.03532891,0.04346908,-0.08087593,0.02946321,0.07196455,0.03468477,-0.01391089,-0.0219962,-0.01652893,0.06092586,-0.00352468,0.00762711,0.00379351,-0.0154131,0.01979197,0.03004391,0.03336873,-0.04115589,-0.06211543,-0.00760002,0.04734345,0.01705403,0.10021437,0.12757289,0.08263509],"last_embed":{"hash":"0c3dd77631514e6dcd620057b55f3620267dcb89edc4aa9e2f8ac53cd08aa707","tokens":416}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.06620894,0.00326759,-0.02303442,-0.02650644,0.01050505,-0.02880851,0.06341499,0.01472531,-0.02789849,-0.03949224,-0.04626093,-0.02340641,-0.08982363,-0.05029405,0.05863907,0.07098556,0.00655692,0.01479365,0.06073847,-0.03843066,-0.02353115,0.04872445,0.05936811,-0.00788833,-0.02076813,-0.00336905,0.00299403,-0.02798456,0.033749,0.07426228,0.08289128,-0.0254679,0.02507464,0.01229717,-0.06065508,-0.05693259,0.02324891,-0.0369069,-0.00168895,0.01779027,-0.03399306,0.01139956,-0.00551952,0.03988754,-0.05106027,-0.01432054,-0.03054371,-0.03428975,-0.00709044,-0.0015502,0.03763823,0.02254036,-0.0140441,0.01657382,0.01011149,0.04769878,-0.0337762,-0.04766745,-0.00072442,-0.00596796,0.02921934,-0.04663434,0.06823432,0.03493899,0.02352824,-0.004534,-0.0190612,-0.00590464,-0.01401434,-0.03142304,-0.05540223,-0.03507837,0.00199246,0.04667316,0.06767285,-0.01169914,0.06441215,0.00045321,0.04983227,0.02600385,-0.06452461,0.0206857,-0.01776822,0.01956704,-0.01179529,0.00989777,0.09411725,-0.06506698,0.04710499,-0.00103741,-0.00608722,0.00419441,-0.02259861,-0.05930649,0.03969489,-0.02163839,0.05659033,-0.01273701,0.01520677,-0.01549901,-0.01217968,-0.00106054,-0.06798652,-0.00077662,-0.03374651,-0.09692119,0.00750915,0.05165426,-0.02689898,0.00377373,0.00831829,0.01362397,-0.02823772,0.02967915,0.0237329,0.04870693,-0.03749943,0.00662268,0.00443864,0.0509938,0.01242187,0.04197713,0.03327071,-0.04323447,-0.04190759,0.00011737,-0.03946587,-0.02878984,0.04010262,0.02688407,-0.01747625,0.05203167,-0.03506871,-0.02681347,0.05464311,0.01756844,0.02680833,-0.03845859,0.0631571,-0.04649099,0.01945476,-0.02074465,-0.0253612,0.00645768,-0.03941355,0.01311274,-0.02489233,-0.06208543,-0.04878734,0.04815702,0.06184999,0.0251909,-0.0390011,-0.04486383,-0.00494435,-0.01443651,-0.00054776,0.01144305,0.01767275,-0.0114767,-0.00387797,0.03724797,-0.00155349,-0.00140695,-0.01014028,-0.00687012,0.0127144,0.0440762,0.02047694,0.02499542,0.00818187,0.02648512,-0.00848681,0.0204653,0.01904983,-0.06482049,0.05814113,0.05298011,-0.03832422,-0.02444279,0.03166022,-0.05735291,-0.01111683,0.0559812,0.01642847,0.02538118,-0.03880483,0.02274861,0.01163855,0.01055016,0.05137399,0.03442612,0.01855289,0.03104397,-0.01614903,0.02008475,0.02272015,-0.08886264,-0.02564213,0.03580113,0.02092232,-0.00158746,-0.0258083,-0.08500981,0.01507754,-0.02420115,-0.01829755,-0.00472168,-0.03784197,0.06965253,-0.04711403,0.00241397,0.01124645,0.01511998,-0.0370261,0.03474586,-0.0169837,-0.00185401,0.01578887,-0.01530454,0.00949906,-0.04328725,-0.07186638,-0.02169424,-0.05224377,-0.02260692,-0.01029212,0.00126926,0.03524486,0.00891828,-0.04229855,0.00600072,-0.02185582,-0.00051639,-0.01659292,-0.01579492,0.0619919,0.01819493,0.01251017,0.00427583,0.01198801,0.04445605,0.02627408,-0.00265826,-0.02590395,-0.03116802,-0.04950477,0.04186272,0.01541138,0.01931635,0.01690325,0.02688808,-0.00889467,0.04611053,-0.00576897,-0.07803577,-0.02007153,-0.00558883,-0.0044121,-0.03420746,0.03158192,-0.00916528,-0.06458268,0.01659974,-0.02426492,-0.04174457,-0.01840894,-0.00232214,-0.04826044,0.05177544,-0.02786164,-0.03303047,0.09732958,-0.03409807,-0.01655903,0.01166748,0.00418011,0.00023794,-0.03358697,-0.01025554,-0.03128577,0.0004525,0.00604948,-0.0028907,-0.07223164,0.06397529,0.0165896,-0.04258748,0.04132665,-0.01210732,0.01823895,-0.01627601,-0.0392289,0.05048275,-0.05662559,-0.04038417,-0.01361649,0.04441874,-0.01420367,-0.04377936,0.02332652,0.07367889,0.00576021,-0.0431382,0.00825136,0.01641075,0.00403403,0.04362015,-0.02512856,-0.04056832,0.03542723,-0.05649866,0.04562966,0.03253547,0.05335804,0.06021814,0.0089834,-0.02819394,-0.05055751,0.0375957,-0.0757889,0.00089219,-0.01991464,0.00014619,0.01714349,-0.05776855,0.0166604,0.00275057,0.08875599,0.0216938,-0.004307,-0.00222025,0.01835435,-0.01191992,-0.00505374,-0.0376574,-0.0248566,-0.03448436,0.00873071,-0.00918021,0.00670713,-0.03690407,-0.00068807,-0.08731282,-0.02811189,0.02826205,-0.01369059,0.00012264,-0.02514151,-0.08525455,0.03875915,0.01120675,-0.02543746,0.02187977,0.04421295,-0.03367951,-0.05312986,-0.03210794,0.00030674,0.01424371,-0.02911039,-0.03402749,-0.01456375,-0.04714186,-0.0014198,0.01422704,0.05483298,0.0294992,-0.00138424,-0.06285494,0.01834605,-0.00876392,-0.02589634,-0.01775119,0.01458451,-0.01056005,-0.02677014,0.01667698,0.06276949,-0.02614606,-0.02733807,-0.03295276,0.03691865,-0.02717428,-0.03619988,-0.03338031,0.03324534,-0.01583773,-0.09349804,-0.08592784,-0.03525915,0.02096689,-0.04339195,0.03760728,0.03292758,-0.03480513,0.03145624,0.01555793,-0.02349371,-0.04432684,0.01216982,-0.04288609,0.06258869,0.00545548,0.00193215,0.06127199,-0.05916303,0.02880013,-0.05719955,-0.03309139,0.01451104,0.0171369,0.03687897,0.04021581,0.04860065,0.01706123,0.00511572,-0.07993206,0.00064713,0.03936596,0.01465711,-0.01287088,0.02590942,0.06207989,-0.01370154,-0.01547552,-0.03863194,0.00639562,-0.05448926,0.02402917,-0.03328552,-0.00605358,-0.02090931,0.01928427,-0.00831862,-0.04434728,0.04574257,0.01312182,0.06626286,0.0211526,-0.05957536,0.02177811,-0.02888769,0.03500619,0.07505486,0.0989539,-0.0296052,-0.07627548,-0.00819735,-0.00717101,-0.0175407,-0.02700595,-0.04677552,0.00325218,0.00288394,-0.01268041,0.02033329,-0.04086858,0.02364162,-0.02072959,-0.02872069,-0.04611912,0.03017832,0.01418402,0.02148969,0.06558903,-0.00064831,0.01243101,-0.02884616,-0.00123617,-0.02307404,0.01628562,-0.00290667,-0.02100261,0.02633009,-0.00037507,0.03014614,0.00434089,-0.0655042,0.02602188,-0.04512271,0.02983746,-0.02961746,-0.02978213,-0.03022012,-0.00203914,0.00669216,0.01016359,0.0324913,-0.01936939,-0.01572529,0.04197613,0.03738278,0.02002344,0.01385719,-0.02954032,0.01065721,0.05610045,-0.00516863,0.07582401,0.00719535,0.03285611,0.01095455,0.04092118,-0.01380678,0.02286714,-0.02855385,0.01012114,0.03150434,-0.0002753,-0.02604481,-0.09890568,0.04381373,0.07184708,-0.0318989,-0.03787295,0.01929129,-0.01767286,0.00062557,-0.0359734,-0.02958956,0.03441362,-0.04063508,-0.02180664,-0.00832878,-0.00544052,-0.00555163,0.06593812,0.06506563,-0.02588288,0.00996002,0.04625165,0.01284462,-0.01538544,0.04121189,-0.04567828,0.04506957,-0.01772629,-0.03211623,0.01140277,0.0677497,0.03438229,0.01707154,0.02763828,-0.03492663,-0.04676417,0.03197512,-0.00172246,0.04887424,-0.0524418,0.01606796,0.02497708,-0.0054245,-0.02743732,-0.07091142,0.01156796,0.00089706,-0.02574573,-0.01505567,0.02699706,-0.01980722,0.052536,-0.02187509,-0.01904058,-0.05549416,-0.01946766,-0.05180006,0.0164903,0.01689949,0.01648023,-0.09756605,-0.01084936,0.03807154,0.00484188,-0.02946355,-0.03476829,0.00462161,-0.03117096,0.02892413,-0.03342757,-0.04824837,0.01006283,0.02903934,0.02843794,-0.01542134,-0.02191584,0.03173934,-0.01480269,0.02924301,-0.01321731,-0.01491,-0.01268775,0.03503746,-0.00821741,-0.05396624,-0.03917801,-0.00959163,0.01792672,-0.01206789,-0.00796804,-0.0071696,-0.0220663,0.01731068,-0.00592993,0.01906813,-0.08865133,-0.02271549,-0.06377423,0.04265276,0.08053382,-0.0022177,0.01719395,0.02615761,0.00792039,0.02547762,-0.06445371,-0.00086123,0.03072441,-0.02688897,-0.01605125,-0.02788184,-0.09506471,-0.05411024,-0.00397964,0.04802596,0.05087764,-0.00098613,0.02057376,0.08793311,-0.05054531,-0.09650429,-0.03822151,-0.02206879,0.02398345,0.01024239,0.05225046,-0.00106147,-0.00882596,-0.01003985,-0.00841794,0.02099377,-0.01369738,-0.01860981,-0.01498105,0.03085916,-0.08534592,-0.02862865,-0.04636876,0.06977174,-0.01447339,0.01203201,-0.02854874,0.00090042,0.01169874,-0.03673618,0.05426349,-0.03024265,0.00596546,0.01559663,0.02206914,-0.0039474,-0.03782445,-0.04243582,-0.0097672,-0.03300242,0.02579951,-0.0177588,0.01847925,-0.04751519,0.03516046,-0.04499974,0.03940692,-0.04491671,0.01570873,-0.03158403,0.03955757,0.01885579,0.0559152,-0.04081559,-0.02844593,-0.05718825,0.00672831,0.01086227,0.01838197,0.02513747,-0.03086425,-0.00255312,0.00382201,0.01084142,-0.00099809,-0.03675716,0.03680068,0.00222067,0.01796487,-0.04388127,-0.04729309,0.02817605,-0.07122406,-0.00493163,-0.00523768,-0.02936574,0.00895235,0.00683268,0.01807356,-0.03595877,-0.03543453,0.01514775,0.01476613,0.04352697,0.01859037,-0.05498433,0.05535777,-0.01186999,0.00870773,0.00133164,-0.0394052,-0.02669134,0.07292057,-0.04101872,0.00316006,0.01848444,0.01634534,0.00599303,0.0155405,-0.00201012,-0.03960577,0.00499093,0.00149979,0.00547553,0.02106011,-0.01883366,-0.0690629,0.00714382,0.05618105,0.04273354,0.0014213,0.04573914,-0.00501766,0.0560451,0.00600395,0.06088787,0.00294124,-0.03515238,0.03599898,-0.03235586,0.07224993,0.01488444,0.00032224,0.03600146,0.01083716,0.04855766,0.06052323,0.01230455,0.10598227,-0.00746199,0.06920496,-0.00413897,-0.05367523,0.00000102,-0.04284136,0.03936649,0.06005852,-0.01661418,-0.00640535,-0.01412977,-0.05855663,-0.00530611,-0.01005334],"last_embed":{"tokens":253,"hash":"eypkxl"}}},"last_read":{"hash":"eypkxl","at":1751335173237},"class_name":"SmartSource","outlinks":[],"metadata":{"aliases":["file upload","文件上传漏洞"],"tags":["网络安全/漏洞/文件上传"],"类型":["漏洞"],"文档更新日期":"2023-12-20"},"blocks":{"#---frontmatter---":[1,10],"#简介":[11,20],"#简介#{1}":[12,13],"#简介#{2}":[14,15],"#简介#{3}":[16,19],"#简介#{4}":[20,20],"#漏洞形成的原因":[21,37],"#漏洞形成的原因#{1}":[23,23],"#漏洞形成的原因#{2}":[24,24],"#漏洞形成的原因#{3}":[25,25],"#漏洞形成的原因#{4}":[26,28],"#漏洞形成的原因#{5}":[29,33],"#漏洞形成的原因#{6}":[34,37]},"last_import":{"mtime":1715930118181,"size":1046,"at":1749024987637,"hash":"eypkxl"},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/文件上传利用.md","last_embed":{"hash":"eypkxl","at":1751335173237}},